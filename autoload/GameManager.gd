extends Node

# Singleton pre riadenie hry
signal chapter_completed(chapter_number: int)
signal puzzle_completed(chapter_number: int, puzzle_number: int)

# Stav hry
var current_chapter: int = 1
var completed_chapters: Array[int] = []
var completed_puzzles: Dictionary = {} # {chapter_number: [puzzle1, puzzle2]}
var last_played_chapter: int = 1
var last_story_phase: int = 0
var last_dialogue_index: int = 0
var current_game_state: String = "story"  # "story", "dialogue", "puzzle"
var current_puzzle_name: String = ""
var current_dialogue_data: Array = []
var is_continuing_game: bool = false  # Flag pre rozlíšenie nová hra vs pokračovanie
var game_settings: Dictionary = {
	"master_volume": 1.0,
	"music_volume": 1.0,
	"sfx_volume": 1.0,
	"fullscreen": false
}

# Informácie o kapitolách
var chapter_info: Dictionary = {
	1: {
		"title": "Kapitola 1: Záhadný začiatok",
		"description": "Marec 1894. Cesta k zámku Van Helsinga cez karpatské horstvo. Rozlúštite Van Helsingove šifry a nájdite cestu k zámku.",
		"puzzles": ["Van Helsingova šifra", "Cesta lesom"]
	},
	2: {
		"title": "Kapitola 2: Hlbšie do temnoty",
		"description": "Brána zámku. Dokážte, že patríte k Rádu a vstúpte do Van Helsingovho sídla.",
		"puzzles": ["Krvavý nápis", "Skúška Rádu"]
	},
	3: {
		"title": "Kapitola 3: Pátranie v zámku",
		"description": "Vstúpte do Van Helsingovho zámku a nájdite stopy po jeho zmiznutí.",
		"puzzles": ["Obrátená správa", "Jednoduchý výpočet"]
	},
	4: {
		"title": "Kapitola 4: Tajné krídlo",
		"description": "Staré krídlo zámku plné pascí a alchymistických tajomstiev.",
		"puzzles": ["Pamäťový test", "Vampírska aritmetika"]
	},
	5: {
		"title": "Kapitola 5: Krypty",
		"description": "Zostup do pradávnych krypt plných tajomstiev a nebezpečenstiev.",
		"puzzles": ["Kód z tieňov", "Tri páky"]
	},
	6: {
		"title": "Kapitola 6: Konfrontácia",
		"description": "Finálny súboj s grófkou Isabelle Báthoryovou. Osud sveta je vo vašich rukách.",
		"puzzles": ["Tri sestry", "Rytmus rituálu"]
	},
	7: {
		"title": "Epilóg: Záchrana mentora",
		"description": "Záchrana doktora Van Helsinga a návrat do Budapešti. Prekliate dedičstvo je definitívne zlomené.",
		"puzzles": []
	}
}

func _ready():
	load_game_data()

# Navigácia medzi scénami
func go_to_main_menu():
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func go_to_chapters():
	get_tree().change_scene_to_file("res://scenes/NewChaptersMenu.tscn")

func go_to_chapter(chapter_number: int):
	current_chapter = chapter_number
	last_played_chapter = chapter_number
	last_story_phase = 0  # Reset story phase when starting new chapter
	is_continuing_game = false  # Nová kapitola, nie pokračovanie
	save_game_data()  # Save progress immediately
	print("Načítavam kapitolu ", chapter_number, " zo súboru: res://scenes/Chapter", chapter_number, ".tscn")
	get_tree().change_scene_to_file("res://scenes/Chapter" + str(chapter_number) + ".tscn")

func go_to_settings():
	get_tree().change_scene_to_file("res://scenes/NewSettingsMenu.tscn")

func go_to_about():
	get_tree().change_scene_to_file("res://scenes/NewAboutGame.tscn")

# Správa progresu
func complete_puzzle(chapter_number: int, puzzle_number: int):
	if not completed_puzzles.has(chapter_number):
		completed_puzzles[chapter_number] = []

	if puzzle_number not in completed_puzzles[chapter_number]:
		completed_puzzles[chapter_number].append(puzzle_number)
		puzzle_completed.emit(chapter_number, puzzle_number)

	# Ak sú dokončené oba hlavolamy, kapitola je hotová
	# Kapitola 7 (Epilóg) nemá hlavolamy
	if chapter_number == 7 or completed_puzzles[chapter_number].size() >= 2:
		complete_chapter(chapter_number)

# Špeciálna funkcia pre dokončenie epilógu
func complete_epilogue():
	complete_chapter(7)

func complete_chapter(chapter_number: int):
	if chapter_number not in completed_chapters:
		completed_chapters.append(chapter_number)
		chapter_completed.emit(chapter_number)
	save_game_data()

func is_chapter_unlocked(chapter_number: int) -> bool:
	if chapter_number == 1:
		return true
	# Pre testovanie - odomknúť kapitoly 2, 3, 4, 5, 6 a 7
	if chapter_number == 2 or chapter_number == 3 or chapter_number == 4 or chapter_number == 5 or chapter_number == 6 or chapter_number == 7:
		return true
	return (chapter_number - 1) in completed_chapters

func is_puzzle_completed(chapter_number: int, puzzle_number: int) -> bool:
	if not completed_puzzles.has(chapter_number):
		return false
	return puzzle_number in completed_puzzles[chapter_number]

# Ukladanie a načítavanie
func save_game_data():
	var save_data = {
		"completed_chapters": completed_chapters,
		"completed_puzzles": completed_puzzles,
		"last_played_chapter": last_played_chapter,
		"last_story_phase": last_story_phase,
		"last_dialogue_index": last_dialogue_index,
		"current_game_state": current_game_state,
		"current_puzzle_name": current_puzzle_name,
		"current_dialogue_data": current_dialogue_data,
		"game_settings": game_settings
	}

	var save_file = FileAccess.open("user://savegame.save", FileAccess.WRITE)
	if save_file:
		save_file.store_string(JSON.stringify(save_data))
		save_file.close()

func load_game_data():
	var save_file = FileAccess.open("user://savegame.save", FileAccess.READ)
	if save_file:
		var json_string = save_file.get_as_text()
		save_file.close()

		var json = JSON.new()
		var parse_result = json.parse(json_string)

		if parse_result == OK:
			var save_data = json.data
			var loaded_chapters = save_data.get("completed_chapters", [])
			completed_chapters.assign(loaded_chapters)
			completed_puzzles = save_data.get("completed_puzzles", {})
			last_played_chapter = save_data.get("last_played_chapter", 1)
			last_story_phase = save_data.get("last_story_phase", 0)
			last_dialogue_index = save_data.get("last_dialogue_index", 0)
			current_game_state = save_data.get("current_game_state", "story")
			current_puzzle_name = save_data.get("current_puzzle_name", "")
			current_dialogue_data = save_data.get("current_dialogue_data", [])
			game_settings = save_data.get("game_settings", game_settings)

# Nastavenia
func update_setting(setting_name: String, value):
	game_settings[setting_name] = value
	apply_settings()
	save_game_data()

func apply_settings():
	# Aplikovanie nastavení
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("Master"),
		linear_to_db(game_settings.master_volume))

	if game_settings.fullscreen:
		DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
	else:
		DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)

# Systém pokračovania hry
func has_save_game() -> bool:
	return FileAccess.file_exists("user://savegame.save")

func get_continue_chapter() -> int:
	if not has_save_game():
		return 1

	# Ak je posledná hraná kapitola dokončená, pokračuj na ďalšiu
	if last_played_chapter in completed_chapters:
		var next_chapter = last_played_chapter + 1
		if next_chapter <= 7:  # Máme 7 kapitol
			return next_chapter
		else:
			return 7  # Zostať na epilógu

	# Inak pokračuj na poslednej hranej kapitole
	return last_played_chapter

func continue_game():
	var continue_chapter = get_continue_chapter()
	# Nastaviť flag že pokračujeme v hre (nie nová hra)
	is_continuing_game = true
	current_chapter = continue_chapter
	last_played_chapter = continue_chapter
	print("🎮 Pokračujem v hre - kapitola: ", continue_chapter, ", stav: ", current_game_state)
	print("🎮 Nastavujem current_chapter a last_played_chapter na: ", continue_chapter)
	get_tree().change_scene_to_file("res://scenes/Chapter" + str(continue_chapter) + ".tscn")

func update_story_progress(chapter: int, story_phase: int, dialogue_index: int = 0):
	last_played_chapter = chapter
	last_story_phase = story_phase
	last_dialogue_index = dialogue_index
	save_game_data()

# Nové funkcie pre tracking presného stavu
func set_game_state_story():
	"""Nastaví stav hry na story mode"""
	current_game_state = "story"
	current_puzzle_name = ""
	current_dialogue_data = []
	save_game_data()

func set_game_state_dialogue(dialogue_data: Array, dialogue_index: int = 0):
	"""Nastaví stav hry na dialogue mode s konkrétnymi dátami"""
	current_game_state = "dialogue"
	current_dialogue_data = dialogue_data
	last_dialogue_index = dialogue_index
	current_puzzle_name = ""
	save_game_data()

func set_game_state_puzzle(puzzle_name: String):
	"""Nastaví stav hry na puzzle mode"""
	current_game_state = "puzzle"
	current_puzzle_name = puzzle_name
	current_dialogue_data = []
	save_game_data()

func restore_exact_game_state():
	"""Obnoví presný stav hry po načítaní kapitoly"""
	print("🔄 Obnovujem stav hry: ", current_game_state)

	# Počkať na načítanie scény
	await get_tree().process_frame
	await get_tree().process_frame

	var current_scene = get_tree().current_scene
	if not current_scene:
		print("❌ Chyba: Žiadna aktuálna scéna")
		return

	match current_game_state:
		"dialogue":
			print("💬 Obnovujem dialóg na pozícii: ", last_dialogue_index)
			restore_dialogue_state(current_scene)
		"puzzle":
			print("🧩 Obnovujem puzzle: ", current_puzzle_name)
			restore_puzzle_state(current_scene)
		"story":
			print("📖 Obnovujem story mode")
			restore_story_state(current_scene)

func restore_dialogue_state(scene):
	"""Obnoví dialóg na správnej pozícii"""
	var dialogue_system = scene.get_node("DialogueSystem")
	if dialogue_system and current_dialogue_data.size() > 0:
		# Nastaviť kapitolu pre správne audio
		dialogue_system.set_current_chapter(last_played_chapter)
		dialogue_system.restore_dialogue_position(current_dialogue_data, last_dialogue_index)

func restore_puzzle_state(scene):
	"""Obnoví puzzle stav"""
	# Nájsť a otvoriť správny puzzle
	if current_puzzle_name != "":
		var puzzle_scene = load("res://scenes/" + current_puzzle_name + ".tscn")
		if puzzle_scene:
			var puzzle_instance = puzzle_scene.instantiate()
			scene.add_child(puzzle_instance)

func restore_story_state(scene):
	"""Obnoví story stav na správnej fáze"""
	# Nastaviť správnu story_phase
	if scene.has_method("set_story_phase"):
		scene.set_story_phase(last_story_phase)
