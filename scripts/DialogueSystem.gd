extends Control
class_name DialogueSystem

signal dialogue_finished
signal dialogue_advanced
signal background_change_requested(image_path: String)

# Import font utilities
const FontLoader = preload("res://scripts/font_loader.gd")

@onready var dialogue_panel: NinePatchRect = $DialoguePanel
@onready var speaker_label: Label = $DialoguePanel/MainContainer/HeaderContainer/SpeakerLabel
@onready var text_label: RichTextLabel = $DialoguePanel/MainContainer/TextContainer/TextLabel
@onready var continue_button: TextureButton = $DialoguePanel/MainContainer/ButtonContainer/ContinueButton
@onready var continue_label: Label = $DialoguePanel/MainContainer/ButtonContainer/ContinueButton/ContinueLabel
@onready var main_menu_button: TextureButton = $DialoguePanel/MainContainer/ButtonContainer/MainMenuButton
@onready var main_menu_label: Label = $DialoguePanel/MainContainer/ButtonContainer/MainMenuButton/MainMenuLabel
@onready var narrator_audio_player: AudioStreamPlayer = $NarratorAudioPlayer
@onready var character_portrait: TextureRect = $DialoguePanel/MainContainer/HeaderContainer/CharacterPortrait

var current_dialogue: Array[Dictionary] = []
var current_line_index: int = 0
var is_typing: bool = false
var typing_speed: float = 0.1
var current_text_words: PackedStringArray = []
var current_word_index: int = 0

# Audio mapovanie pre kapitoly
var chapter1_narrator_audio_map: Dictionary = {}
var chapter2_audio_map: Dictionary = {}  # Pre rozprávača aj Viktora
var current_chapter: int = 0

# Mapovanie postáv na portréty
var character_portraits: Dictionary = {
	"Rozprávač": "res://assets/Avatary/Rozpravac.png",
	"Viktor": "res://assets/Avatary/Viktor.png",
	"Kočiš": "res://assets/Avatary/Kocis.png",
	"Van Helsing": "res://assets/Avatary/VanHelsing.png",
	"Isabelle": "res://assets/Avatary/Isabelle.png"
}

# Mapovanie dialógov na hudbu podľa promptu
var dialogue_music_triggers: Dictionary = {
	# KAPITOLA 1: Storm Journey (úvod) → Forest of Shadows (les)
	"Marec 1894. Studený dážď bičuje okná kočiara kolísajúceho sa na úzkej ceste cez karpatské horstvo.": "storm_journey",
	"Srdce vám takmer zastane: 'Grófka je v krypte.'": "forest_shadows",

	# KAPITOLA 2: Castle Gates
	"Stojíte pred masívnou železnou bránou zdobenou heraldickými symbolmi.": "castle_gates",

	# KAPITOLA 3: Library of Secrets
	"Vstupujete do veľkej haly osvetlenej len plamenným mihotaním krbu.": "library_secrets",

	# KAPITOLA 4: Alchemy Lab (už implementované cez start_chapter_4)
	"Staré krídlo pôsobí ako vstup do iného sveta. Vzduch je ťažší, studenší.": "alchemy_lab",

	# KAPITOLA 5: Descent into Darkness → Ancient Crypts
	"Zostupujete po úzkom kamennom schodisku do hlbín zámku.": "descent_darkness",

	# KAPITOLA 6: Isabelle's Awakening → Final Ritual (cez final_battle)
	"V strede miestnosti stojí mramorový sarkofág.": "isabelle_awakening",

	# EPILÓG: Van Helsing Rescue
	"Keď Isabelle zmizne, jedna zo stien sa pomaly odsunie.": "van_helsing_rescue",
	"Stúpate späť chodbami zámku. Viktor vás čaká s úľavou v očiach.": "van_helsing_rescue"
}

func _ready():
	hide()

	# Bezpečné pripojenie signálov
	if continue_button and is_instance_valid(continue_button):
		continue_button.pressed.connect(_on_continue_pressed)
	if main_menu_button and is_instance_valid(main_menu_button):
		main_menu_button.pressed.connect(_on_main_menu_pressed)

	# Nastavenie štýlu dialógového panelu
	if dialogue_panel and is_instance_valid(dialogue_panel):
		dialogue_panel.modulate = Color(1, 1, 1, 0.95)

	# Inicializácia audio mapovania pre kapitoly
	initialize_chapter1_audio_mapping()
	initialize_chapter2_audio_mapping()

	# Kontrola dostupnosti všetkých potrebných nodov
	validate_node_references()

	# Aplikovanie fontov a farieb
	apply_dialogue_fonts()

func validate_node_references():
	"""Kontrola dostupnosti všetkých potrebných nodov"""
	var missing_nodes = []

	if not dialogue_panel or not is_instance_valid(dialogue_panel):
		missing_nodes.append("dialogue_panel")
	if not speaker_label or not is_instance_valid(speaker_label):
		missing_nodes.append("speaker_label")
	if not text_label or not is_instance_valid(text_label):
		missing_nodes.append("text_label")
	if not continue_button or not is_instance_valid(continue_button):
		missing_nodes.append("continue_button")
	if not continue_label or not is_instance_valid(continue_label):
		missing_nodes.append("continue_label")
	if not character_portrait or not is_instance_valid(character_portrait):
		missing_nodes.append("character_portrait")

	if missing_nodes.size() > 0:
		print("⚠️ DialogueSystem: Chýbajúce nody: ", missing_nodes)
	else:
		print("✅ DialogueSystem: Všetky nody sú dostupné")

func initialize_chapter1_audio_mapping():
	# Mapovanie textov rozprávača na audio súbory pre kapitolu 1
	# Rozprávač rozpráva len v úvodných dialógoch a interlude dialógoch (nie v hlavolamoch)
	chapter1_narrator_audio_map = {
		# Úvodné dialógy rozprávača (8 dialógov s audiom, posledný bez audia)
		"Marec 1894. Studený dážď bičuje okná kočiara kolísajúceho sa na úzkej ceste cez karpatské horstvo.": "res://audio/ZVUKY_Kapitola_1/rozpravac_001.mp3",
		"Búrka silnie s každou míľou, ktorou sa približujete k zámku Van Helsinga.": "res://audio/ZVUKY_Kapitola_1/rozpravac_002.mp3",
		"V kočiari sedíte už štyri hodiny, študujúc posledné poznámky svojho mentora.": "res://audio/ZVUKY_Kapitola_1/rozpravac_003.mp3",
		"Vzduch je napätý – nielen kvôli blížiacej sa búrke, ale aj preto, že cítite hrozbu niečoho omnoho nebezpečnejšieho.": "res://audio/ZVUKY_Kapitola_1/rozpravac_004.mp3",
		"Van Helsingov posledný telegram bol nezvyčajne stručný a naliehavý. To nie je jeho štýl – určite sa deje niečo vážne.": "res://audio/ZVUKY_Kapitola_1/rozpravac_005.mp3",
		"Vonku zúri živelný chaos: vietor vyje v korunách stromov ako tisíc duší v mukách.": "res://audio/ZVUKY_Kapitola_1/rozpravac_006.mp3",
		"Každý blesk na okamih osvetlí zahmlenú krajinu, akoby samotná príroda odmietala odhaliť svoje tajomstvá.": "res://audio/ZVUKY_Kapitola_1/rozpravac_007.mp3",
		"Kočiš je nervózny – už hodinu neprehovoril ani slovo.": "res://audio/ZVUKY_Kapitola_1/rozpravac_008.mp3",
		# "Van Helsingov list obsahuje zašifrovanú správu..." - BEZ AUDIA (pred hádankou)

		# Interlude dialógy rozprávača po prvom hlavolame (8 dialógov)
		"Srdce vám takmer zastane: 'Grófka je v krypte.'": "res://audio/ZVUKY_Kapitola_1/rozpravac_009.mp3",
		"Znamená to, že Van Helsing našiel, čo hľadal – Isabelle Báthoryovú, poslednú z prekliatej línie.": "res://audio/ZVUKY_Kapitola_1/rozpravac_010.mp3",
		"Prečo však nepríde za vami? Prečo vás volá na zámok? A prečo je jeho list taký ustráchaný?": "res://audio/ZVUKY_Kapitola_1/rozpravac_011.mp3",
		"Blesk, prudké zastavenie kočiara.": "res://audio/ZVUKY_Kapitola_1/rozpravac_012.mp3",

		# Kočiš dialógy (4 repliky)
		"To je všetko! Ďalej nejdem, páni!": "res://audio/Kocis/kocis_001.mp3",
		"Ani korunu by ste mi nedali, aby som šiel bližšie k tomu prekliatemu miestu!": "res://audio/Kocis/kocis_002.mp3",
		"Hovorí sa, že kto sa priblíži k zámku po západe slnka, už nikdy neuvidí úsvit.": "res://audio/Kocis/kocis_003.mp3",
		"Musíte pokračovať pešo. Nech vás Boh ochraňuje...": "res://audio/Kocis/kocis_004.mp3",

		# Pokračovanie interlude dialógov rozprávača
		"Kočiš vám hodí batožinu do blata a bez slova odcvála.": "res://audio/ZVUKY_Kapitola_1/rozpravac_013.mp3",
		"Jeho kočiar mizne v hukote búrky. Ostávate sami – uprostred karpatskej divočiny, s nocou, ktorá sa rýchlo blíži.": "res://audio/ZVUKY_Kapitola_1/rozpravac_014.mp3",
		"Vaše kroky sa zabárajú do hlbokého blata.": "res://audio/ZVUKY_Kapitola_1/rozpravac_015.mp3",
		"Les pôsobí ako živá bytosť – počuť šepot medzi konármi, praskot vetvičiek, vzdialené vytie, ktoré neznie celkom vlčie.": "res://audio/ZVUKY_Kapitola_1/rozpravac_016.mp3",
		"Hmla klesá a každý strom pripomína strážcu sledujúceho vaše pohyby.": "res://audio/ZVUKY_Kapitola_1/rozpravac_017.mp3",
		"Cesta sa rozdeľuje na štyri smery. Van Helsingove poznámky obsahujú zvláštnu básničku.": "res://audio/ZVUKY_Kapitola_1/rozpravac_018.mp3",
		"V denníku je poznámka: 'Východ, potom dvakrát na západ, nakoniec sever - tak sa dostaneš k zámku.'": "res://audio/ZVUKY_Kapitola_1/rozpravac_019.mp3"
	}

func initialize_chapter2_audio_mapping():
	# Mapovanie textov pre kapitolu 2 - rozprávač aj Viktor
	chapter2_audio_map = {
		# Úvodné dialógy rozprávača (5 dialógov)
		"Stojíte pred masívnou železnou bránou zdobenou heraldickými symbolmi.": "res://audio/Kapitola_2/rozpravac_022.mp3",
		"Každý erb rozpráva príbeh – moc, pád dynastie, vzostup i úpadok.": "res://audio/Kapitola_2/rozpravac_023.mp3",
		"Brána je zamknutá, v oknách nevidno svetlo. Zámok pôsobí opustene.": "res://audio/Kapitola_2/rozpravac_024.mp3",
		"Havrany krákajú zo strešných ríms, akoby sa vysmievali vašej situácii.": "res://audio/Kapitola_2/rozpravac_025.mp3",
		"Na bráne je krvou napísaný text: 'MOJE MENO JE SMRŤ, ALE POZADU SOM ŽIVOT'": "res://audio/Kapitola_2/rozpravac_026.mp3",

		# Interlude dialógy po prvom puzzle (3 dialógy rozprávača + 5 Viktora)
		"Mechanizmus zaškrípe a brána sa pomaly otvára.": "res://audio/Kapitola_2/rozpravac_027.mp3",
		"Vstupujete na nádvorie – dlažba je pokrytá machom a medzi kameňmi prerastá tráva.": "res://audio/Kapitola_2/rozpravac_028.mp3",
		"Nádvorie je tiché ako hrob. Ticho prerušuje len kvapkanie vody zo žľabov.": "res://audio/Kapitola_2/rozpravac_029.mp3",
		# "Kdesi buchnú dvere..." - bez audia

		# Viktor dialógy
		"Vy... ste to vy?": "res://audio/Kapitola_2/Viktor_2.mp3",
		"Telegram odišiel pred troma dňami. Doktor vás čaká každú noc pri lampe.": "res://audio/Kapitola_2/Viktor_3.mp3",
		"Ale teraz nie je čas na spomienky.": "res://audio/Kapitola_2/Viktor_4.mp3",
		"Nie všetci, čo prichádzajú, patria medzi živých. Ak ste členovia Rádu, odpovedzte.": "res://audio/Kapitola_2/Viktor_5.mp3",
		"Tri otázky. Tri odpovede. Každá z iného sveta – kov, hviezda, bylina.": "res://audio/Kapitola_2/Viktor_6.mp3",

		# Záverečné dialógy Viktora po druhom puzzle
		"Tak predsa. Svetlo ešte úplne nezhaslo.": "res://audio/Kapitola_2/Viktor_7.mp3"
		# "Poďte. A nech vás nočné tiene nespoznajú ako hostí." - Viktor_8.mp3 neexistuje
	}

func set_current_chapter(chapter_number: int):
	current_chapter = chapter_number

func start_dialogue(dialogue_data: Array[Dictionary]):
	current_dialogue = dialogue_data
	current_line_index = 0
	# Uložiť stav dialógu
	GameManager.set_game_state_dialogue(dialogue_data, 0)
	show()
	display_current_line()

func display_current_line():
	if current_line_index >= current_dialogue.size():
		end_dialogue()
		return

	var line_data = current_dialogue[current_line_index]
	var speaker = line_data.get("speaker", "Rozprávač")
	var text = line_data.get("text", "")

	# Kontrola pre zmenu pozadia na konkrétnu vetu
	if text == "Vstupujete do rozľahlej knižnice. Regály siahajú až k stropu.":
		background_change_requested.emit("res://assets/pozadia/Kapitola_3/3.png")
	elif text == "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami.":
		background_change_requested.emit("res://assets/pozadia/Kapitola_4/1.png")
	elif text == "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi.":
		background_change_requested.emit("res://assets/pozadia/Kapitola_5/3.png")
	elif text == "Isabelle vystúpi zo sarkofágu. Už nie je tou kráskou z kroník.":
		background_change_requested.emit("res://assets/pozadia/Kapitola_6/3.png")

	# Prehrávanie audio pre rozprávača, kočiša a Viktora v kapitolách 1 a 2
	print("🎵 Audio check - speaker: ", speaker, ", current_chapter: ", current_chapter)
	if (speaker == "Rozprávač" or speaker == "Kočiš") and current_chapter == 1:
		print("🎵 Spúšťam audio pre kapitolu 1")
		play_narrator_audio(text)
	elif (speaker == "Rozprávač" or speaker == "Viktor") and current_chapter == 2:
		print("🎵 Spúšťam audio pre kapitolu 2")
		play_narrator_audio(text)
	else:
		print("🎵 Audio sa nespúšťa - nesprávna kapitola alebo speaker")

	if speaker_label and is_instance_valid(speaker_label):
		speaker_label.text = speaker

	# Zobrazenie portrétu postavy
	show_character_portrait(speaker)

	# Kontrola či treba zmeniť hudbu na základe dialógu
	check_dialogue_music_trigger(text)

	# Aplikovanie správneho fontu podľa typu postavy
	apply_character_font(speaker)

	# Animácia písania textu
	if text_label and is_instance_valid(text_label):
		text_label.text = ""
	is_typing = true
	if continue_button and is_instance_valid(continue_button):
		continue_button.disabled = true

	var full_text = line_data.get("text", "")
	type_text(full_text)

func type_text(text: String):
	if not text_label or not is_instance_valid(text_label):
		print("⚠️ text_label nie je dostupný v type_text()")
		return

	text_label.text = ""

	# Rozdelenie textu na slová
	current_text_words = text.split(" ")
	current_word_index = 0

	# Horizontálne odvíjanie po slovách
	for i in range(current_text_words.size()):
		if not is_typing:
			break

		current_word_index = i
		var displayed_text = ""

		# Zobraziť slová od aktuálneho slova
		for j in range(i + 1):
			if j > 0:
				displayed_text += " "
			displayed_text += current_text_words[j]

		if text_label and is_instance_valid(text_label):
			text_label.text = displayed_text

		await get_tree().create_timer(typing_speed).timeout

	is_typing = false
	if continue_button and is_instance_valid(continue_button):
		continue_button.disabled = false
		continue_button.grab_focus()

func _on_continue_pressed():
	if is_typing:
		# Preskočiť animáciu písania
		is_typing = false
		if text_label and is_instance_valid(text_label) and current_line_index < current_dialogue.size():
			text_label.text = current_dialogue[current_line_index].get("text", "")
		if continue_button and is_instance_valid(continue_button):
			continue_button.disabled = false
		return

	current_line_index += 1
	dialogue_advanced.emit()

	# Uložiť progress po každom dialógu
	if current_chapter > 0:
		GameManager.update_story_progress(current_chapter, current_line_index)

	display_current_line()

func play_narrator_audio(text: String):
	# Prehrá audio pre rozprávača, kočiša alebo Viktora v kapitolách 1 a 2
	print("🎵 play_narrator_audio volané s textom: ", text.substr(0, 50), "...")
	var audio_path = ""

	# Kontrola audio máp pre rôzne kapitoly
	if chapter1_narrator_audio_map.has(text):
		audio_path = chapter1_narrator_audio_map[text]
		print("🎵 Našiel som audio v chapter1_map: ", audio_path)
	elif chapter2_audio_map.has(text):
		audio_path = chapter2_audio_map[text]
		print("🎵 Našiel som audio v chapter2_map: ", audio_path)

	if audio_path != "" and narrator_audio_player:
		print("🎵 Pokúšam sa načítať audio: ", audio_path)
		var audio_stream = load(audio_path)
		if audio_stream:
			narrator_audio_player.stream = audio_stream
			narrator_audio_player.play()
			print("✅ Prehrávam audio: ", audio_path)
		else:
			print("❌ CHYBA: Nemožno načítať audio súbor: ", audio_path)
	else:
		print("❌ Audio pre text nenájdené alebo narrator_audio_player chýba")
		print("   - audio_path: ", audio_path)
		print("   - narrator_audio_player: ", narrator_audio_player)

func end_dialogue():
	# Zastaviť audio ak hrá
	if narrator_audio_player and narrator_audio_player.playing:
		narrator_audio_player.stop()
	# Skryť portrét postavy
	show_character_portrait("")
	hide()
	print("🎬 DialogueSystem: Emitujem dialogue_finished signál")
	dialogue_finished.emit()

func _input(event):
	if visible and event.is_action_pressed("ui_accept"):
		_on_continue_pressed()
	elif visible and event.is_action_pressed("ui_cancel"):
		skip_dialogue()

func skip_dialogue():
	is_typing = false
	end_dialogue()

# Prednastavené dialógy pre rozprávača
# Funkcie pre získanie dialógov podľa fázy príbehu
func get_chapter_intro_dialogue(chapter_number: int) -> Array[Dictionary]:
	if chapter_number == 1:
		return [
			{"speaker": "Rozprávač", "text": "Marec 1894. Studený dážď bičuje okná kočiara kolísajúceho sa na úzkej ceste cez karpatské horstvo."},
			{"speaker": "Rozprávač", "text": "Búrka silnie s každou míľou, ktorou sa približujete k zámku Van Helsinga."},
			{"speaker": "Rozprávač", "text": "V kočiari sedíte už štyri hodiny, študujúc posledné poznámky svojho mentora."},
			{"speaker": "Rozprávač", "text": "Vzduch je napätý – nielen kvôli blížiacej sa búrke, ale aj preto, že cítite hrozbu niečoho omnoho nebezpečnejšieho."},
			{"speaker": "Rozprávač", "text": "Van Helsingov posledný telegram bol nezvyčajne stručný a naliehavý. To nie je jeho štýl – určite sa deje niečo vážne."},
			{"speaker": "Rozprávač", "text": "Vonku zúri živelný chaos: vietor vyje v korunách stromov ako tisíc duší v mukách."},
			{"speaker": "Rozprávač", "text": "Každý blesk na okamih osvetlí zahmlenú krajinu, akoby samotná príroda odmietala odhaliť svoje tajomstvá."},
			{"speaker": "Rozprávač", "text": "Kočiš je nervózny – už hodinu neprehovoril ani slovo."},
			{"speaker": "Rozprávač", "text": "Van Helsingov list obsahuje zašifrovanú správu. Jeho poznámka znie: 'Pamätaj - každé písmeno posuniem o jedno miesto dopredu.'"}
		]
	elif chapter_number == 2:
		return [
			{"speaker": "Rozprávač", "text": "Stojíte pred masívnou železnou bránou zdobenou heraldickými symbolmi."},
			{"speaker": "Rozprávač", "text": "Každý erb rozpráva príbeh – moc, pád dynastie, vzostup i úpadok."},
			{"speaker": "Rozprávač", "text": "Brána je zamknutá, v oknách nevidno svetlo. Zámok pôsobí opustene."},
			{"speaker": "Rozprávač", "text": "Havrany krákajú zo strešných ríms, akoby sa vysmievali vašej situácii."},
			{"speaker": "Rozprávač", "text": "Na bráne je krvou napísaný text: 'MOJE MENO JE SMRŤ, ALE POZADU SOM ŽIVOT'"}
		]
	elif chapter_number == 3:
		return [
			{"speaker": "Rozprávač", "text": "Vstupujete do veľkej haly osvetlenej len plamenným mihotaním krbu."},
			{"speaker": "Rozprávač", "text": "Steny zdobí zbierka starodávnych zbraní – meče, kopije, kuše."},
			{"speaker": "Rozprávač", "text": "V rohu tikajú staré hodiny; ich monotónne klikanie je jediné, čo narúša ticho."},
			{"speaker": "Viktor", "text": "Pán doktor odišiel včera večer krátko po západe slnka."},
			{"speaker": "Viktor", "text": "Povedal iba: 'Viktor, idem preskúmať staré krídlo zámku. Ak sa do úsvitu nevrátim, pošli telegram Rádu.'"},
			{"speaker": "Viktor", "text": "Odvtedy o ňom niet správy."},
			{"speaker": "Viktor", "text": "Do toho krídla nik nevošiel celé desaťročia. Je to najstaršia časť zámku."}
		]
	elif chapter_number == 4:
		return [
			{"speaker": "Rozprávač", "text": "Staré krídlo pôsobí ako vstup do iného sveta. Vzduch je ťažší, studenší."},
			{"speaker": "Rozprávač", "text": "Steny pokrýva vlhkosť a medzi kameňmi rastú čudné plesne."},
			{"speaker": "Rozprávač", "text": "Občas počuť mechanické cvakanie – v múroch stále pracujú skryté mechanizmy."},
			{"speaker": "Viktor", "text": "Počkajte! Spomínam si na básničku."},
			{"speaker": "Viktor", "text": "'Kráčaj, kde Mesiac svieti, nie tam, kde Slnko horí.' V núdzi ju vraj použijem."}
		]
	elif chapter_number == 5:
		return [
			{"speaker": "Rozprávač", "text": "Za laboratóriom sa skrýva úzke kamenné schodisko vedúce do hlbín."},
			{"speaker": "Rozprávač", "text": "Kamene sú ošúchané tisíckami krokov, no roky tu nik nebol."},
			{"speaker": "Rozprávač", "text": "Z hĺbky sála chlad a vlhkosť, pripomínajúce dych hrobky."},
			{"speaker": "Viktor", "text": "Počkám tu a budem strážiť ústup!"},
			{"speaker": "Viktor", "text": "Ak sa niečo stane, kričte. A vezmite si toto..."},
			{"speaker": "Viktor", "text": "Kríž požehnal sám ostrihomský arcibiskup. Môže vám zachrániť život."}
		]
	elif chapter_number == 6:
		return [
			{"speaker": "Rozprávač", "text": "Keď sa posledná pečať uvoľní, celý sarkofág sa otrasie."},
			{"speaker": "Rozprávač", "text": "Kameň praská pod tlakom ohromnej sily zvnútra."},
			{"speaker": "Rozprávač", "text": "Vzduch naplní pach smrti. Z trhlín sa valí biela para."},
			{"speaker": "Isabelle", "text": "Konečne... po troch storočiach je moja väznica otvorená."},
			{"speaker": "Isabelle", "text": "Starý hlupák Van Helsing poslúžil ako dokonalá návnada."},
			{"speaker": "Isabelle", "text": "A vy ste jeho dielo dokonali lepšie, než som dúfala."},
			{"speaker": "Isabelle", "text": "Ste z Rádu Striebornej ruže, však? Vidím vo vašich očiach strach i odhodlanie."}
		]
	elif chapter_number == 7:
		return [
			{"speaker": "Rozprávač", "text": "Keď Isabelle zmizne, jedna zo stien sa pomaly odsunie."},
			{"speaker": "Rozprávač", "text": "Za ňou je úzka komora. Na zemi, spútaný reťazami, no živý, leží doktor Van Helsing."},
			{"speaker": "Rozprávač", "text": "Je bledý a slabý, no jeho oči sa rozjasnia, keď vás zazrie."},
			{"speaker": "Van Helsing", "text": "Vedel som... vedel som, že prídete."},
			{"speaker": "Van Helsing", "text": "Moji najlepší žiaci by ma nenechali v pazúroch monštra."},
			{"speaker": "Van Helsing", "text": "Isabelle ma uhryzla hneď prvú noc. Jed koluje v mojich žilách a premená ma..."},
			{"speaker": "Van Helsing", "text": "Cítim to. Každou hodinou som bližšie k tomu, čím bola ona."},
			{"speaker": "Van Helsing", "text": "Váš elixír – spomalil proces. Získal mi čas."},
			{"speaker": "Rozprávač", "text": "Podávate mu fľaštičku s elixírom. Van Helsing ju vypije jedným dúškom."},
			{"speaker": "Van Helsing", "text": "Ďakujem. Jed sa spomaľuje, no na úplné vyliečenie potrebujem viac."},
			{"speaker": "Van Helsing", "text": "Musíme do môjho laboratória v Budapešti. Tam dokončíme rituál očistenia."},
			{"speaker": "Van Helsing", "text": "Zachránili ste mňa, aj Európu. Keby sa Isabelle dostala na slobodu..."},
			{"speaker": "Van Helsing", "text": "Ale je po všetkom. Teraz sa musíme sústrediť na moje uzdravenie."}
		]

	return [{"speaker": "Rozprávač", "text": "Kapitola sa načítava..."}]

# Dialógy medzi hlavolamami
func get_interlude_dialogue(chapter_number: int, phase: int) -> Array[Dictionary]:
	if chapter_number == 1:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Srdce vám takmer zastane: 'Grófka je v krypte.'"},
				{"speaker": "Rozprávač", "text": "Znamená to, že Van Helsing našiel, čo hľadal – Isabelle Báthoryovú, poslednú z prekliatej línie."},
				{"speaker": "Rozprávač", "text": "Prečo však nepríde za vami? Prečo vás volá na zámok? A prečo je jeho list taký ustráchaný?"},
				{"speaker": "Rozprávač", "text": "Blesk, prudké zastavenie kočiara."},
				{"speaker": "Kočiš", "text": "To je všetko! Ďalej nejdem, páni!"},
				{"speaker": "Kočiš", "text": "Ani korunu by ste mi nedali, aby som šiel bližšie k tomu prekliatemu miestu!"},
				{"speaker": "Kočiš", "text": "Hovorí sa, že kto sa priblíži k zámku po západe slnka, už nikdy neuvidí úsvit."},
				{"speaker": "Kočiš", "text": "Musíte pokračovať pešo. Nech vás Boh ochraňuje..."},
				{"speaker": "Rozprávač", "text": "Kočiš vám hodí batožinu do blata a bez slova odcvála."},
				{"speaker": "Rozprávač", "text": "Jeho kočiar mizne v hukote búrky. Ostávate sami – uprostred karpatskej divočiny, s nocou, ktorá sa rýchlo blíži."},
				{"speaker": "Rozprávač", "text": "Vaše kroky sa zabárajú do hlbokého blata."},
				{"speaker": "Rozprávač", "text": "Les pôsobí ako živá bytosť – počuť šepot medzi konármi, praskot vetvičiek, vzdialené vytie, ktoré neznie celkom vlčie."},
				{"speaker": "Rozprávač", "text": "Hmla klesá a každý strom pripomína strážcu sledujúceho vaše pohyby."},
				{"speaker": "Rozprávač", "text": "Cesta sa rozdeľuje na štyri smery. Van Helsingove poznámky obsahujú zvláštnu básničku."},
				{"speaker": "Rozprávač", "text": "V denníku je poznámka: 'Východ, potom dvakrát na západ, nakoniec sever - tak sa dostaneš k zámku.'"}
			]
	elif chapter_number == 2:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Mechanizmus zaškrípe a brána sa pomaly otvára."},
				{"speaker": "Rozprávač", "text": "Vstupujete na nádvorie – dlažba je pokrytá machom a medzi kameňmi prerastá tráva."},
				{"speaker": "Rozprávač", "text": "Nádvorie je tiché ako hrob. Ticho prerušuje len kvapkanie vody zo žľabov."},
				{"speaker": "Rozprávač", "text": "Kdesi buchnú dvere – možno ich otvoril vietor, možno niečo iné."},
				{"speaker": "Viktor", "text": "Vy... ste to vy?"},
				{"speaker": "Viktor", "text": "Telegram odišiel pred troma dňami. Doktor vás čaká každú noc pri lampe."},
				{"speaker": "Viktor", "text": "Ale teraz nie je čas na spomienky."},
				{"speaker": "Viktor", "text": "Nie všetci, čo prichádzajú, patria medzi živých. Ak ste členovia Rádu, odpovedzte."},
				{"speaker": "Viktor", "text": "Tri otázky. Tri odpovede. Každá z iného sveta – kov, hviezda, bylina."}
			]
	elif chapter_number == 3:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Viktor", "text": "Vonkajšie múry! Samozrejme!"},
				{"speaker": "Viktor", "text": "Tam, kde sa najstaršie základy zámku spájajú s novou stavbou."},
				{"speaker": "Viktor", "text": "Viem presne, kde to je - pri severozápadnej veži."},
				{"speaker": "Rozprávač", "text": "Vstupujete do rozľahlej knižnice. Regály siahajú až k stropu."},
				{"speaker": "Rozprávač", "text": "Knihy sú v rôznych jazykoch – latinčina, nemčina, rumunčina, maďarčina."},
				{"speaker": "Rozprávač", "text": "Väčšina diel sa venuje tematikám, ktoré nie sú pre slabé povahy: démonológia, vampirológia, alchýmia."},
				{"speaker": "Rozprávač", "text": "Za tapisériou rodokmeňa odhaľujete tajnú priehradku."},
				{"speaker": "Rozprávač", "text": "Ukrytá je v nej drobná kožená knižka – osobný denník Van Helsinga."}
			]
	elif chapter_number == 4:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Úspech! Prešli ste bez spustenia mechanizmov."},
				{"speaker": "Rozprávač", "text": "Na konci chodby stoja masívne dubové dvere."},
				{"speaker": "Rozprávač", "text": "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami."},
				{"speaker": "Rozprávač", "text": "Stoly sú obsypané sklenenými nádobami, kotlíkmi a čudesnými prístrojmi."},
				{"speaker": "Viktor", "text": "Toto je recept na ochranný elixír!"},
				{"speaker": "Viktor", "text": "Doktor mi ho ukázal pred mesiacom."},
				{"speaker": "Viktor", "text": "Povedal: 'Viktor, ak ma pôjdeš hľadať do katakomb, priprav si tento elixír.'"}
			]
	elif chapter_number == 5:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi."},
				{"speaker": "Rozprávač", "text": "V strede stojí kamenný podstavec. Rozhadzané ležia doktorove veci."},
				{"speaker": "Rozprávač", "text": "Rozbitá lampa, prázdny strieborný revolver, kožený notes s roztrhanými stranami."},
				{"speaker": "Rozprávač", "text": "Na kameňoch tmavnú škvrny, čo nápadne pripomínajú krv. No doktora niet."},
				{"speaker": "Van Helsing", "text": "Je tu! Isabelle má prisluhovačov – nie upírov, ale niečo horšie."},
				{"speaker": "Van Helsing", "text": "Ako vedela, že prídem? Musím sa dostať k jej sarkofágu..."},
				{"speaker": "Van Helsing", "text": "Voda dochádza, striebro tiež... sú ich príliš veľa..."},
				{"speaker": "Van Helsing", "text": "Ak toto niekto nájde, dokončite, čo som začal..."},
				{"speaker": "Rozprávač", "text": "Za podstavcom odkrývate tajné dvere vedúce do rozľahlej sály."},
				{"speaker": "Rozprávač", "text": "Uprostred stojí mohutný sarkofág zdobený tromi pákami."}
			]
	elif chapter_number == 6:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Isabelle", "text": "Bystré! No múdrosť vás neochráni pred mojou silou."},
				{"speaker": "Isabelle", "text": "Tri storočia som sa živila snami o pomste."},
				{"speaker": "Rozprávač", "text": "Isabelle vystúpi zo sarkofágu. Už nie je tou kráskou z kroník."},
				{"speaker": "Rozprávač", "text": "Stáročia magického spánku ju premenili na niečo horšie než smrť."},
				{"speaker": "Rozprávač", "text": "Van Helsingove poznámky! Musíte vykonať rituál presne podľa nich."}
			]

	return []

func get_final_dialogue(chapter_number: int) -> Array[Dictionary]:
	if chapter_number == 7:
		return [
			{"speaker": "Rozprávač", "text": "Stúpate späť chodbami zámku. Viktor vás čaká s úľavou v očiach."},
			{"speaker": "Rozprávač", "text": "Keď vidí Van Helsinga živého, takmer sa rozplače."},
			{"speaker": "Viktor", "text": "Pán doktor! Vďaka Bohu, že žijete!"},
			{"speaker": "Viktor", "text": "Už som sa obával najhoršieho."},
			{"speaker": "Viktor", "text": "Okamžite pripravím kočiar na cestu do Budapešti!"},
			{"speaker": "Van Helsing", "text": "Áno, Viktor. Musíme vyraziť čo najskôr."},
			{"speaker": "Van Helsing", "text": "Času je dosť, ale nesmieme ho premárniť."},
			{"speaker": "Van Helsing", "text": "A vy, moji priatelia... dokázali ste to."},
			{"speaker": "Rozprávač", "text": "Opúšťate zámok práve keď prvé lúče slnka prenikajú cez mraky."},
			{"speaker": "Rozprávač", "text": "Búrka sa skončila. Vzduch je čistý a svieži."},
			{"speaker": "Rozprávač", "text": "Za vami zostáva zámok, ktorý už nikdy nebude domovom zla."},
			{"speaker": "Rozprávač", "text": "Pred vami je cesta do Budapešti a nádej na úplné uzdravenie vášho mentora."},
			{"speaker": "Rozprávač", "text": "Prekliate dedičstvo rodu Báthoryovcov je definitívne zlomené."},
			{"speaker": "Rozprávač", "text": "Váš príbeh sa končí, ale legenda o vašom hrdinskom čine bude žiť večne."}
		]

	return []

func get_puzzle_intro_dialogue(chapter_number: int, puzzle_number: int) -> Array[Dictionary]:
	if chapter_number == 1:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Zašifrovaná správa: 'HSÔGLB KF X LSZQUF'"}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Musíte nájsť správnu cestu k zámku."}
			]
	elif chapter_number == 2:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Rozlúštite krvavý nápis na bráne."}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Dokážte, že patríte k Rádu."}
			]
	elif chapter_number == 3:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Na stole nájdete Van Helsingov denník. Posledná stránka obsahuje zvláštnu správu."}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "V denníku je poznámka o Isabelle Báthoryovej."}
			]
	elif chapter_number == 4:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Na stene blikne krátka sekvencia 3 farieb. Musíte ju zopakovať."}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Na tabuli sú tri rovnice s vampírskymi symbolmi."}
			]
	elif chapter_number == 5:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "V miestnosti sú 4 sviečky vrhajúce tiene s číslami: 7, 3, 9, 5."},
				{"speaker": "Rozprávač", "text": "Na podlahe je nápis: 'Súčet páru je tucet'"}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Na stene je vyškriabaný odkaz: 'Kráčaj, kde mesiac svieti, nie tam, kde slnko horí. Hviezda ti ukáže cestu.'"}
			]
	elif chapter_number == 6:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Na stene sú tri portréty sestier s menovkami: Mária, Anna, Isabelle."},
				{"speaker": "Rozprávač", "text": "Pod portrétmi je starý pergamen s tromi výrokmi."},
				{"speaker": "Van Helsing", "text": "Len jedna hovorí pravdu. Tá, čo klame, je vinná."}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Van Helsingov rituál napísaný krvou: 'Raz-raz-dva-dva-raz'"},
				{"speaker": "Rozprávač", "text": "Máte 4 symboly: kríž, voda, oheň, soľ"}
			]

	return [
		{"speaker": "Rozprávač", "text": "Pred vami sa objavuje nový hlavolam..."}
	]

func get_puzzle_success_dialogue(chapter_number: int = 0, puzzle_number: int = 0) -> Array[Dictionary]:
	if chapter_number == 1 and puzzle_number == 2:
		return [
			{"speaker": "Rozprávač", "text": "Konečne! Cez koruny stromov sa črtajú obrysy mohutných veží."},
			{"speaker": "Rozprávač", "text": "Zámok Van Helsinga sa týči pred vami ako čierna silueta proti búrlivej oblohe."},
			{"speaker": "Rozprávač", "text": "Táto stavba z 13. storočia v dnešnú noc pripomína skôr hrobku dávno mŕtvych kráľov než domov živého človeka."}
		]
	elif chapter_number == 2 and puzzle_number == 2:
		return [
			{"speaker": "Viktor", "text": "Tak predsa. Svetlo ešte úplne nezhaslo."},
			{"speaker": "Viktor", "text": "Poďte. A nech vás nočné tiene nespoznajú ako hostí."}
		]
	elif chapter_number == 3 and puzzle_number == 2:
		return [
			{"speaker": "Viktor", "text": "15. marca 1894 – Konečne!"},
			{"speaker": "Viktor", "text": "Po štyroch rokoch neúnavného pátrania som našiel dôkaz, že grófka Isabelle Báthoryová prežila svoju údajnú smrť."},
			{"speaker": "Viktor", "text": "Oficiálne záznamy sú prepracovaná lož."},
			{"speaker": "Viktor", "text": "Našiel som ju! Skrýva sa v kryptách pod týmto zámkom."},
			{"speaker": "Viktor", "text": "18. marca – Pripravujem sa na zostup do katakomb."},
			{"speaker": "Viktor", "text": "Potrebujem: strieborné gule, svätenú vodu z Ríma, dubový kôl, kríž môjho starého otca."},
			{"speaker": "Viktor", "text": "19. marca – Viktor netuší, kam idem. Tak je to lepšie."},
			{"speaker": "Viktor", "text": "Ak sa nevrátim do úsvitu, nech privolá Rád."},
			{"speaker": "Viktor", "text": "Krypty! Nik tam nevkročil celé storočia."},
			{"speaker": "Viktor", "text": "Vchod je zapečatený. Čo to pán doktor robil?"}
		]
	elif chapter_number == 4 and puzzle_number == 2:
		return [
			{"speaker": "Rozprávač", "text": "Elixír v fľaštičke jemne zažiari striebristým svetlom."},
			{"speaker": "Viktor", "text": "Výborne! Teraz sa môžeme odvážiť do katakomb."},
			{"speaker": "Viktor", "text": "Nezabudnite – elixír chráni len hodinu."}
		]
	elif chapter_number == 5 and puzzle_number == 2:
		return [
			{"speaker": "Rozprávač", "text": "Sarkofág sa pomaly otvára s hlbokým kamenným škrípotom."},
			{"speaker": "Rozprávač", "text": "Zvnútra sa ozýva slabý vzdych... alebo je to len vietor?"},
			{"speaker": "Rozprávač", "text": "Tajomstvo grófky Isabelle Báthoryovej je konečne odhalené."},
			{"speaker": "Rozprávač", "text": "Ale to je už príbeh pre ďalšiu kapitolu..."}
		]
	elif chapter_number == 6 and puzzle_number == 2:
		return [
			{"speaker": "Isabelle", "text": "Nie! Čo robíte? Ten rituál – to je nemožné!"},
			{"speaker": "Isabelle", "text": "Nik nepozná staré spôsoby!"},
			{"speaker": "Rozprávač", "text": "Kruh zo svätenej vody zažiari. Sviece tvoria geometrický znak moci."},
			{"speaker": "Rozprávač", "text": "Isabelle reve, no jej hlas už znie porazene."},
			{"speaker": "Rozprávač", "text": "Strieborný kríž padne do stredu kruhu; ozve sa zvuk, akoby sa trhal závoj medzi svetmi."},
			{"speaker": "Rozprávač", "text": "Isabelle sa rozpadá na prach, ktorý víri a mizne v svetle rituálu."},
			{"speaker": "Rozprávač", "text": "Posledný výkrik stíchne. Po troch storočiach je definitívne mŕtva."},
			{"speaker": "Rozprávač", "text": "Hra je dokončená! Gratulujeme k víťazstvu nad temnotou!"}
		]

	var success_messages = [
		[{"speaker": "Rozprávač", "text": "Výborne! Úspešne ste vyriešili hlavolam."}],
		[{"speaker": "Rozprávač", "text": "Bravó! Vaša logika vás neviedla."}],
		[{"speaker": "Rozprávač", "text": "Skvelé! Ďalšia záhada je odhalená."}]
	]
	return success_messages[randi() % success_messages.size()]

func get_puzzle_hint_dialogue(chapter_number: int = 0, puzzle_number: int = 0, hint_level: int = 1) -> Array[Dictionary]:
	if chapter_number == 1 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Skúste posunúť každé písmeno o jedno miesto dozadu v abecede."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Napríklad H sa stane G, S sa stane R."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Prvé slovo je GRÓFKA. Pokračujte ďalej."}]
	elif chapter_number == 1 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Sledujte presne poradie v poznámke."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Prvý smer je východ (V)."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Celé poradie: Východ, Západ, Západ, Sever."}]
	elif chapter_number == 2 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Čítaj pozorne - niekedy je odpoveď v opaku."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Smrť pozadu... čo dostaneš, keď otočíš slovo?"}]
			3:
				return [{"speaker": "Rozprávač", "text": "SMRŤ odzadu je TRMS, ale nezabudni na piatu literu z 'SOM' → TRMSO"}]
	elif chapter_number == 2 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Viktor", "text": "Tri svety - kov, hviezda, bylina. Každý má svoju moc proti temnote."}]
			2:
				return [{"speaker": "Viktor", "text": "Kov čistí duše, hviezda ovláda vody, bylina chráni hroby."}]
			3:
				return [{"speaker": "Viktor", "text": "Striebro, Mesiac, Cesnak - základy každého lovca upírov."}]
	elif chapter_number == 3 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Van Helsing píše, že píše slová odzadu. Čo to znamená?"}]
			2:
				return [{"speaker": "Rozprávač", "text": "Skúste čítať každé slovo pozpätku."}]
			3:
				return [{"speaker": "Rozprávač", "text": "DOP = POD, IMÍŠJAKNOV = VONKAJŠÍMI..."}]
	elif chapter_number == 3 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Je to jednoduchá matematika."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Od roku sobáša odpočítajte vek."}]
			3:
				return [{"speaker": "Rozprávač", "text": "1596 - 20 = ?"}]
	elif chapter_number == 4 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Sústreďte sa a zapamätajte si poradie farieb."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Bola to sekvencia troch farieb."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Skúste znova a dávajte lepší pozor."}]
	elif chapter_number == 4 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Každý symbol skrýva číslo. Začnite s netopierom."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Ak dva netopiere dávajú 16, koľko je jeden netopier?"}]
			3:
				return [{"speaker": "Rozprávač", "text": "Netopier=8, Krv=11, Rakva=7. Spočítajte ich."}]
	elif chapter_number == 5 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Tiene nikdy neklamú, ale musíš ich správne spárovať."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Tucet je 12. Ktoré dve čísla dávajú dokopy 12?"}]
			3:
				return [{"speaker": "Rozprávač", "text": "7+5=12 a 3+9=12. Zadaj všetky štyri čísla: 7539"}]
	elif chapter_number == 5 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Odkaz si pamätáš? 'Kde Mesiac svieti...'"}]
			2:
				return [{"speaker": "Rozprávač", "text": "Slnko horí - tam nechoď. Mesiac svieti - tam choď."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Postupuj podľa básničky: najprv Mesiac, potom Hviezda."}]
	elif chapter_number == 6 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Ak len jedna hovorí pravdu, skúste postupne predpokladať, že pravdu hovorí každá z nich."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Overte každú možnosť: vedie k logickému sporu alebo nie?"}]
			3:
				return [{"speaker": "Rozprávač", "text": "Ak Mária hovorí pravdu, Anna je nevinná a Isabelle nezradila. Mária je správna odpoveď!"}]
	elif chapter_number == 6 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "'Raz' a 'dva' označujú poradie symbolov."}]
			2:
				return [{"speaker": "Rozprávač", "text": "'Raz' = prvý symbol (kríž), 'dva' = druhý symbol (voda)."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Stlačte: prvý, prvý, druhý, druhý, prvý."}]

	var hints = [
		[{"speaker": "Rozprávač", "text": "Možno by ste sa mali pozrieť na problém z iného uhla..."}],
		[{"speaker": "Rozprávač", "text": "Pamätajte, nie vždy je prvé riešenie to správne."}],
		[{"speaker": "Rozprávač", "text": "Skúste sa sústrediť na detaily, ktoré ste možno prehliadli."}]
	]
	return hints[randi() % hints.size()]

func apply_character_font(speaker: String):
	"""Aplikuje správny font podľa typu postavy"""
	if not text_label:
		return

	# Aplikovanie fontu podľa postavy pomocou vylepšeného FontLoader systému
	if speaker == "Rozprávač":
		# Rozprávačský text - Crimson Text, italic, svetlá sivá
		FontLoader.apply_font_style(text_label, "narrator_text")
	else:
		# Dialógy postav - Cormorant Garamond, krémová
		FontLoader.apply_font_style(text_label, "character_dialogue")

	# Responzívne škálovanie pre mobil
	if text_label and is_instance_valid(text_label):
		var viewport = get_viewport()
		if viewport and is_instance_valid(viewport):
			var screen_size = viewport.get_visible_rect().size
			var current_size = text_label.get_theme_font_size("normal_font_size")

			if screen_size.x <= 480:
				# Mobilné zariadenia - zmenšiť font
				text_label.add_theme_font_size_override("normal_font_size", int(current_size * 0.8))
			elif screen_size.x <= 768:
				# Tablety - mierne zmenšiť font
				text_label.add_theme_font_size_override("normal_font_size", int(current_size * 0.9))
		else:
			print("⚠️ DialogueSystem: Viewport nie je dostupný pre responzívne škálovanie")

func apply_dialogue_fonts():
	"""Aplikuje fonty a farby na dialogue elementy"""
	# Aplikovanie fontu na continue label
	if continue_label and is_instance_valid(continue_label):
		FontLoader.apply_font_style(continue_label, "ui_elements")
		continue_label.add_theme_color_override("font_color", Color("#D4AF37"))
		continue_label.add_theme_font_size_override("font_size", 18)

	# Aplikovanie fontu na main menu label
	if main_menu_label and is_instance_valid(main_menu_label):
		FontLoader.apply_font_style(main_menu_label, "ui_elements")
		main_menu_label.add_theme_color_override("font_color", Color("#D4AF37"))
		main_menu_label.add_theme_font_size_override("font_size", 18)

func _on_main_menu_pressed():
	"""Návrat do hlavného menu s uložením progresu"""
	print("Návrat do hlavného menu z dialógu")
	# Uložiť aktuálny stav dialógu
	GameManager.set_game_state_dialogue(current_dialogue, current_line_index)
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func restore_dialogue_position(dialogue_data: Array, line_index: int):
	"""Obnoví dialóg na konkrétnej pozícii"""
	print("🔄 Obnovujem dialóg na pozícii: ", line_index, " z ", dialogue_data.size(), " riadkov")

	if dialogue_data.size() == 0:
		print("❌ Žiadne dialogue dáta na obnovenie")
		return

	# Nastaviť aktuálnu kapitolu pre správne audio
	current_chapter = GameManager.last_played_chapter
	print("🎵 Nastavujem current_chapter na: ", current_chapter)

	# Nastaviť dialogue dáta
	current_dialogue = dialogue_data
	current_line_index = line_index

	# Skontrolovať hranice
	if current_line_index >= current_dialogue.size():
		current_line_index = current_dialogue.size() - 1

	if current_line_index < 0:
		current_line_index = 0

	# Zobraziť dialogue panel
	show()
	visible = true

	# Zobraziť aktuálny riadok
	display_current_line()

	print("✅ Dialóg obnovený na pozícii: ", current_line_index)

func show_character_portrait(speaker: String):
	"""Zobrazí portrét postavy podľa mena speakera"""
	if not character_portrait or not is_instance_valid(character_portrait):
		print("⚠️ character_portrait nie je dostupný")
		return

	# Ak je speaker prázdny, skryť portrét
	if speaker == "":
		character_portrait.visible = false
		print("👤 Skrývam portrét postavy")
		return

	# Skontrolovať či máme portrét pre túto postavu
	if character_portraits.has(speaker):
		var portrait_path = character_portraits[speaker]
		var texture = load(portrait_path)
		if texture:
			character_portrait.texture = texture
			character_portrait.visible = true
			print("👤 Zobrazujem portrét pre: ", speaker)
		else:
			character_portrait.visible = false
			print("⚠️ Nemožno načítať portrét pre: ", speaker, " z cesty: ", portrait_path)
	else:
		# Postava nemá portrét - skryť
		character_portrait.visible = false
		print("👤 Žiadny portrét pre: ", speaker)

func check_dialogue_music_trigger(text: String):
	"""Kontroluje či dialóg má spustiť zmenu hudby"""
	if dialogue_music_triggers.has(text):
		var music_track = dialogue_music_triggers[text]
		print("🎵 Dialóg trigger: Spúšťam hudbu '", music_track, "' pre text: ", text.substr(0, 50), "...")
		AudioManager.play_music(music_track)
	else:
		# Kontrola čiastočných zhôd pre dlhšie texty
		for trigger_text in dialogue_music_triggers:
			if text.begins_with(trigger_text.substr(0, 30)):  # Prvých 30 znakov
				var music_track = dialogue_music_triggers[trigger_text]
				print("🎵 Čiastočná zhoda: Spúšťam hudbu '", music_track, "' pre text: ", text.substr(0, 50), "...")
				AudioManager.play_music(music_track)
				break
