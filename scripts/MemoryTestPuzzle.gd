extends Control
class_name MemoryTestPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var status_label: Label = $PuzzlePanel/VBoxContainer/StatusLabel
@onready var red_button: TextureButton = $PuzzlePanel/VBoxContainer/ColorContainer/RedButton
@onready var blue_button: TextureButton = $PuzzlePanel/VBoxContainer/ColorContainer/BlueButton
@onready var green_button: TextureButton = $PuzzlePanel/VBoxContainer/ColorContainer/GreenButton
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var reset_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/ResetButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_sequence: Array[String] = []
var player_sequence: Array[String] = []
var is_showing_sequence: bool = false
var sequence_index: int = 0
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()

	# Nastavenie počiatočných farieb tlačidiel
	if red_button:
		red_button.modulate = Color(0.7, 0.7, 0.7, 1.0)
	if blue_button:
		blue_button.modulate = Color(0.7, 0.7, 0.7, 1.0)
	if green_button:
		green_button.modulate = Color(0.7, 0.7, 0.7, 1.0)

	# Pripojenie signálov
	if red_button:
		red_button.pressed.connect(_on_red_pressed)
	if blue_button:
		blue_button.pressed.connect(_on_blue_pressed)
	if green_button:
		green_button.pressed.connect(_on_green_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if reset_button:
		reset_button.pressed.connect(_on_reset_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)

func show_puzzle():
	show()
	generate_sequence()
	reset_puzzle()
	show_sequence()

func generate_sequence():
	correct_sequence.clear()
	var colors = ["red", "blue", "green"]
	
	# Generuj náhodnú sekvenciu 3 farieb
	for i in range(3):
		correct_sequence.append(colors[randi() % colors.size()])
	
	print("Generovaná sekvencia: ", correct_sequence)

func reset_puzzle():
	player_sequence.clear()
	sequence_index = 0
	is_showing_sequence = false
	enable_buttons(false)
	update_status("Pozorujte sekvenciu...")

func show_sequence():
	is_showing_sequence = true
	enable_buttons(false)
	sequence_index = 0
	update_status("Pozorujte sekvenciu...")
	
	# Spusti animáciu sekvencie
	show_next_color()

func show_next_color():
	if sequence_index >= correct_sequence.size():
		# Sekvencia skončila
		is_showing_sequence = false
		enable_buttons(true)
		update_status("Teraz zopakujte sekvenciu...")
		return
	
	var color = correct_sequence[sequence_index]
	var button = get_color_button(color)
	
	if button:
		# Zvýrazni tlačidlo
		button.modulate = Color.WHITE
		await get_tree().create_timer(0.8).timeout
		
		# Vráť normálnu farbu
		button.modulate = Color(0.7, 0.7, 0.7, 1.0)
		await get_tree().create_timer(0.3).timeout
	
	sequence_index += 1
	show_next_color()

func get_color_button(color: String) -> TextureButton:
	match color:
		"red":
			return red_button
		"blue":
			return blue_button
		"green":
			return green_button
		_:
			return null

func enable_buttons(enabled: bool):
	if red_button:
		red_button.disabled = not enabled
	if blue_button:
		blue_button.disabled = not enabled
	if green_button:
		green_button.disabled = not enabled

func update_status(text: String):
	if status_label:
		status_label.text = text

func _on_red_pressed():
	if not is_showing_sequence:
		add_to_sequence("red")

func _on_blue_pressed():
	if not is_showing_sequence:
		add_to_sequence("blue")

func _on_green_pressed():
	if not is_showing_sequence:
		add_to_sequence("green")

func add_to_sequence(color: String):
	player_sequence.append(color)
	
	# Skontroluj, či je farba správna
	var current_index = player_sequence.size() - 1
	if player_sequence[current_index] != correct_sequence[current_index]:
		# Nesprávna farba
		AudioManager.play_puzzle_error_sound()
		show_error_feedback()
		update_status("Nesprávne! Skúste znova.")
		await get_tree().create_timer(1.5).timeout
		reset_puzzle()
		show_sequence()
		return

	# Skontroluj, či je sekvencia kompletná
	if player_sequence.size() >= correct_sequence.size():
		# Úspech!
		AudioManager.play_puzzle_success_sound()
		update_status("Výborne! Sekvencia je správna!")
		enable_buttons(false)
		await get_tree().create_timer(1.0).timeout
		puzzle_solved.emit()
		hide()
	else:
		update_status("Pokračujte... (" + str(player_sequence.size()) + "/" + str(correct_sequence.size()) + ")")

func show_error_feedback():
	# Červené zablikanie všetkých tlačidiel
	var buttons = [red_button, blue_button, green_button]
	for button in buttons:
		if button:
			button.modulate = Color.RED
	
	var tween = create_tween()
	for button in buttons:
		if button:
			tween.parallel().tween_property(button, "modulate", Color(0.7, 0.7, 0.7, 1.0), 0.5)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Sústreďte sa a zapamätajte si poradie farieb."
		2:
			return "Bola to sekvencia troch farieb."
		3:
			return "Skúste znova a dávajte lepší pozor."
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(text: String):
	# Jednoduchý dialóg s nápoveďou
	update_status("Nápoveda: " + text)
	await get_tree().create_timer(3.0).timeout
	if not is_showing_sequence:
		update_status("Teraz zopakujte sekvenciu...")

func _on_reset_pressed():
	reset_puzzle()
	show_sequence()

func _on_close_pressed():
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
