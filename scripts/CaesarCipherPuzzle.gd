extends Control
class_name CaesarCipherPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var cipher_label: Label = $PuzzlePanel/VBoxContainer/CipherLabel
@onready var input_field: LineEdit = $PuzzlePanel/VBoxContainer/InputContainer/InputField
@onready var submit_button: TextureButton = $PuzzlePanel/VBoxContainer/InputContainer/SubmitButton
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_answer: String = "GRÓFKA JE V KRYPTE"
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()
	
	# Pripo<PERSON><PERSON> signálov
	if submit_button:
		submit_button.pressed.connect(_on_submit_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)
	if input_field:
		input_field.text_submitted.connect(_on_text_submitted)
	
	setup_puzzle()

func setup_puzzle():
	if title_label:
		title_label.text = "Van Helsingova šifra"

	if description_label:
		description_label.text = "[center]Van Helsingov list obsahuje zašifrovanú správu.[/center]\n\n[b]Van Helsingova poznámka:[/b]\n'Pamätaj - každé písmeno posuniem o jedno miesto dopredu.'\n\nRozlúštite správu a zadajte odpoveď:"

	if cipher_label:
		cipher_label.text = "HSÔGLB KF X LSZQUF"
		cipher_label.add_theme_font_size_override("font_size", 24)
		cipher_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER

	if input_field:
		input_field.placeholder_text = "Zadajte rozlúštenú správu..."
		input_field.grab_focus()

func show_puzzle():
	show()
	# Uložiť stav puzzle
	GameManager.set_game_state_puzzle("CaesarCipherPuzzle")
	if input_field:
		input_field.grab_focus()

func _on_submit_pressed():
	check_answer()

func _on_text_submitted(text: String):
	check_answer()

func check_answer():
	if not input_field:
		return
	
	var user_answer = normalize_text(input_field.text)
	var correct_normalized = normalize_text(correct_answer)
	
	if user_answer == correct_normalized:
		AudioManager.play_puzzle_success_sound()
		puzzle_solved.emit()
		hide()
	else:
		# Nesprávna odpoveď
		AudioManager.play_puzzle_error_sound()
		show_error_feedback()

func normalize_text(text: String) -> String:
	# Odstránenie diakritiky a normalizácia
	var normalized = text.to_upper().strip_edges()

	# Základná náhrada diakritiky
	var replacements = {
		"Á": "A", "Ä": "A", "Č": "C", "Ď": "D", "É": "E", "Ě": "E",
		"Í": "I", "Ľ": "L", "Ĺ": "L", "Ň": "N", "Ó": "O", "Ô": "O",
		"Ŕ": "R", "Š": "S", "Ť": "T", "Ú": "U", "Ů": "U", "Ý": "Y",
		"Ž": "Z"
	}

	for old_char in replacements:
		normalized = normalized.replace(old_char, replacements[old_char])

	return normalized

func show_error_feedback():
	if input_field:
		input_field.modulate = Color.RED
		var tween = create_tween()
		tween.tween_property(input_field, "modulate", Color.WHITE, 0.5)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Skúste posunúť každé písmeno o jedno miesto dozadu v abecede."
		2:
			return "Napríklad H sa stane G, S sa stane R."
		3:
			return "Prvé slovo je GRÓFKA. Pokračujte ďalej."
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(hint_text: String):
	var dialog = AcceptDialog.new()
	add_child(dialog)
	dialog.dialog_text = hint_text
	dialog.title = "Nápoveda"
	dialog.popup_centered()
	
	# Automatické odstránenie dialógu
	dialog.confirmed.connect(func(): dialog.queue_free())
	dialog.close_requested.connect(func(): dialog.queue_free())

func _on_close_pressed():
	# Obnoviť story stav
	GameManager.set_game_state_story()
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()

# Funkcia pre dekódovanie Caesarovej šifry (pre testovanie)
func decode_caesar(text: String, shift: int = 1) -> String:
	var result = ""
	var alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	
	for char in text:
		if char == " ":
			result += " "
		elif char in alphabet:
			var index = alphabet.find(char)
			var new_index = (index - shift) % alphabet.length()
			if new_index < 0:
				new_index += alphabet.length()
			result += alphabet[new_index]
		else:
			result += char
	
	return result
