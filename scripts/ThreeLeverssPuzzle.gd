extends Control
class_name ThreeLeverssPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var quote_label: Label = $PuzzlePanel/VBoxContainer/QuoteLabel
@onready var sun_button: TextureButton = $PuzzlePanel/VBoxContainer/LeversContainer/SunButton
@onready var moon_button: TextureButton = $PuzzlePanel/VBoxContainer/LeversContainer/MoonButton
@onready var star_button: TextureButton = $PuzzlePanel/VBoxContainer/LeversContainer/StarButton
@onready var status_label: Label = $PuzzlePanel/VBoxContainer/StatusLabel
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var reset_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/ResetButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_sequence: Array[String] = ["moon", "star"]  # Mesiac → Hviezda
var player_sequence: Array[String] = []
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()
	
	# Pripojenie signálov
	if sun_button:
		sun_button.pressed.connect(_on_sun_pressed)
	if moon_button:
		moon_button.pressed.connect(_on_moon_pressed)
	if star_button:
		star_button.pressed.connect(_on_star_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if reset_button:
		reset_button.pressed.connect(_on_reset_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)

func show_puzzle():
	show()
	reset_puzzle()

func reset_puzzle():
	player_sequence.clear()
	hint_level = 0
	update_status("Postupujte podľa básničky...")
	reset_button_states()

func reset_button_states():
	# Nastavenie normálnych farieb
	if sun_button:
		sun_button.modulate = Color(1.0, 1.0, 1.0, 1.0)
	if moon_button:
		moon_button.modulate = Color(1.0, 1.0, 1.0, 1.0)
	if star_button:
		star_button.modulate = Color(1.0, 1.0, 1.0, 1.0)

func update_status(text: String):
	if status_label:
		status_label.text = text

func _on_sun_pressed():
	# Slnko je vždy chyba
	show_error_feedback()
	update_status("Nesprávne! 'Nie tam, kde slnko horí!'")
	await get_tree().create_timer(2.0).timeout
	reset_puzzle()

func _on_moon_pressed():
	if player_sequence.is_empty():
		# Prvý krok - správne
		player_sequence.append("moon")
		moon_button.modulate = Color.CYAN
		update_status("Správne! Teraz pokračujte...")
		check_sequence()
	else:
		# Mesiac už bol stlačený
		show_error_feedback()
		update_status("Mesiac už bol použitý!")
		await get_tree().create_timer(1.5).timeout
		reset_puzzle()

func _on_star_pressed():
	if player_sequence.size() == 1 and player_sequence[0] == "moon":
		# Druhý krok - správne
		player_sequence.append("star")
		star_button.modulate = Color.YELLOW
		AudioManager.play_puzzle_success_sound()
		update_status("Výborne! Sekvencia je správna!")
		await get_tree().create_timer(1.0).timeout
		puzzle_solved.emit()
		hide()
	else:
		# Hviezda bez mesiaca
		AudioManager.play_puzzle_error_sound()
		show_error_feedback()
		update_status("Najprv musíte stlačiť Mesiac!")
		await get_tree().create_timer(1.5).timeout
		reset_puzzle()

func check_sequence():
	# Kontrola, či je sekvencia kompletná
	if player_sequence.size() >= correct_sequence.size():
		# Sekvencia je kompletná, ale toto sa nedostane, lebo star_button to už vyriešil
		pass

func show_error_feedback():
	# Červené zablikanie všetkých tlačidiel
	var buttons = [sun_button, moon_button, star_button]
	for button in buttons:
		if button:
			button.modulate = Color.RED
	
	var tween = create_tween()
	for button in buttons:
		if button:
			tween.parallel().tween_property(button, "modulate", Color.WHITE, 0.5)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Odkaz si pamätáš? 'Kde Mesiac svieti...'"
		2:
			return "Slnko horí - tam nechoď. Mesiac svieti - tam choď."
		3:
			return "Postupuj podľa básničky: najprv Mesiac, potom Hviezda."
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(text: String):
	# Jednoduchý dialóg s nápoveďou
	update_status("Nápoveda: " + text)
	await get_tree().create_timer(3.0).timeout
	update_status("Postupujte podľa básničky...")

func _on_reset_pressed():
	reset_puzzle()

func _on_close_pressed():
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
