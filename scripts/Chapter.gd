extends Control
class_name Chapter

@export var chapter_number: int = 1
@onready var dialogue_system: DialogueSystem = $DialogueSystem
# @onready var chapter_title: Label = $VBoxContainer/ChapterTitle  # Odstránené - už sa nepouž<PERSON>va
@onready var puzzle1_button: Button = $VBoxContainer/PuzzlesContainer/Puzzle1Button
@onready var puzzle2_button: Button = $VBoxContainer/PuzzlesContainer/Puzzle2Button
# @onready var start_button: Button = $VBoxContainer/StartButton  # Odstránené pre kapitolu 7
@onready var background: NinePatchRect = $Background

# Import font utilities
const ApplyChapterFonts = preload("res://scripts/apply_chapter_fonts.gd")

var current_puzzle: int = 0
var puzzles_completed: Array[bool] = []
var story_phase: int = 0  # 0=úvod, 1=po prvom hlavolame, 2=po druhom hlavolame
var waiting_for_puzzle: bool = false

func _ready():
	print("=== KAPITOLA ", chapter_number, " SA NAČÍTAVA ===")

	# Nastavenie aktuálnej kapitoly v GameManager
	GameManager.current_chapter = chapter_number
	print("🎮 Nastavujem GameManager.current_chapter na: ", chapter_number)

	# Inicializácia puzzles_completed array
	puzzles_completed.assign([false, false])

	# Nastavenie textu
	var chapter_info = GameManager.chapter_info[chapter_number]
	print("Info o kapitole: ", chapter_info)
	# Nadpisy kapitol boli odstránené z UI
	# if chapter_title:
	#	chapter_title.text = chapter_info.title
	#	ApplyChapterFonts.apply_chapter_title_font(chapter_title)

	# Spustenie hudby pre kapitolu
	start_chapter_music()

	# Pre kapitolu 7 (Epilóg) - skryť hlavolamy, automaticky spustiť dialógy
	if chapter_number == 7:
		if puzzle1_button:
			puzzle1_button.visible = false
		if puzzle2_button:
			puzzle2_button.visible = false
		# Automaticky spustiť epilóg
		start_chapter_intro()
	else:
		# Pre ostatné kapitoly - normálne nastavenie
		if chapter_info.puzzles.size() >= 2:
			if puzzle1_button:
				puzzle1_button.text = chapter_info.puzzles[0]
			if puzzle2_button:
				puzzle2_button.text = chapter_info.puzzles[1]

	# Pripojenie signálov - bezpečne
	if puzzle1_button:
		puzzle1_button.pressed.connect(_on_puzzle1_pressed)
	if puzzle2_button:
		puzzle2_button.pressed.connect(_on_puzzle2_pressed)
	# start_button bol odstránený z kapitoly 7

	if dialogue_system:
		print("🔌 Pripájam dialogue_finished signál")
		dialogue_system.dialogue_finished.connect(_on_dialogue_finished)
		dialogue_system.background_change_requested.connect(_on_background_change_requested)
	else:
		print("❌ CHYBA: DialogueSystem nie je dostupný v _ready()")

	# Kontrola stavu hlavolamov
	update_puzzle_buttons()

	# Skrytie tlačidiel hlavolamov na začiatku
	hide_puzzle_buttons()

	# Kontrola či pokračujeme v hre alebo začíname novú
	if GameManager.is_continuing_game and GameManager.has_save_game():
		print("🔄 Pokračujem v uloženej hre")
		# Obnoviť presný stav hry
		GameManager.restore_exact_game_state()
		# Resetovať flag
		GameManager.is_continuing_game = false
	else:
		print("🆕 Začínam novú kapitolu")
		# Spustenie úvodného dialógu pre novú kapitolu
		start_chapter_intro()

	# Nastavenie fokusu na prvý puzzle button
	if puzzle1_button:
		puzzle1_button.grab_focus()

func start_chapter_intro():
	print("Spúšťam úvodné dialógy pre kapitolu ", chapter_number)
	if dialogue_system:
		dialogue_system.set_current_chapter(chapter_number)
		var intro_dialogue = dialogue_system.get_chapter_intro_dialogue(chapter_number)
		print("Počet úvodných dialógov: ", intro_dialogue.size())
		if intro_dialogue.size() > 0:
			print("Prvý dialóg: ", intro_dialogue[0])
		dialogue_system.start_dialogue(intro_dialogue)
	else:
		print("CHYBA: DialogueSystem nie je dostupný!")

func change_background_image(image_path: String):
	if background:
		var new_texture = load(image_path)
		if new_texture:
			background.texture = new_texture
			print("Pozadie zmenené na: ", image_path)
		else:
			print("CHYBA: Nemožno načítať obrázok: ", image_path)

func _on_background_change_requested(image_path: String):
	change_background_image(image_path)

func hide_puzzle_buttons():
	if puzzle1_button:
		puzzle1_button.visible = false
	if puzzle2_button:
		puzzle2_button.visible = false

func show_puzzle_button(puzzle_number: int):
	if puzzle_number == 1 and puzzle1_button:
		puzzle1_button.visible = true
		puzzle1_button.grab_focus()
	elif puzzle_number == 2 and puzzle2_button:
		puzzle2_button.visible = true
		puzzle2_button.grab_focus()

func update_puzzle_buttons():
	# Aktualizovanie stavu tlačidiel na základe dokončených hlavolamov
	puzzles_completed[0] = GameManager.is_puzzle_completed(chapter_number, 1)
	puzzles_completed[1] = GameManager.is_puzzle_completed(chapter_number, 2)

	if puzzles_completed[0] and puzzle1_button:
		if not puzzle1_button.text.ends_with(" ✓"):
			puzzle1_button.text += " ✓"
		puzzle1_button.modulate = Color.GREEN

	if puzzles_completed[1] and puzzle2_button:
		if not puzzle2_button.text.ends_with(" ✓"):
			puzzle2_button.text += " ✓"
		puzzle2_button.modulate = Color.GREEN

func _on_puzzle1_pressed():
	current_puzzle = 1
	if chapter_number == 1 or chapter_number == 2 or chapter_number == 3 or chapter_number == 4 or chapter_number == 5 or chapter_number == 6:
		# Pre kapitoly 1, 2, 3, 4, 5 a 6 - priamo spustiť hlavolam
		show_puzzle_scene(1)
	else:
		start_puzzle(1)

func _on_puzzle2_pressed():
	current_puzzle = 2
	if chapter_number == 1 or chapter_number == 2 or chapter_number == 3 or chapter_number == 4 or chapter_number == 5 or chapter_number == 6:
		# Pre kapitoly 1, 2, 3, 4, 5 a 6 - priamo spustiť hlavolam
		show_puzzle_scene(2)
	else:
		start_puzzle(2)

func start_puzzle(puzzle_number: int):
	# Spustenie dialógu pre hlavolam (pre ostatné kapitoly)
	if dialogue_system:
		dialogue_system.set_current_chapter(chapter_number)
		var puzzle_dialogue = dialogue_system.get_puzzle_intro_dialogue(chapter_number, puzzle_number)
		dialogue_system.start_dialogue(puzzle_dialogue)

func _on_dialogue_finished():
	print("🎬 _on_dialogue_finished volaná! Story phase: ", story_phase, ", Chapter: ", chapter_number)
	if chapter_number == 7:
		# Pre epilóg - zmeniť pozadie a pokračovať v dialógoch
		if story_phase == 0:
			story_phase = 1
			# Zmeniť pozadie na druhý obrázok (záverečné dialógy)
			change_background_image("res://assets/pozadia/Kapitola_7/2.png")
			# Spustiť záverečné dialógy epilógu
			if dialogue_system:
				dialogue_system.set_current_chapter(chapter_number)
				var final_dialogue = dialogue_system.get_final_dialogue(chapter_number)
				dialogue_system.start_dialogue(final_dialogue)
		else:
			# Po všetkých dialógoch automaticky ukončiť hru
			print("Epilóg dokončený - automatický návrat do hlavného menu")
			GameManager.complete_epilogue()

			# Zobrazenie záverečnej správy
			show_completion_message("Hra dokončená! Ďakujeme za hranie 'Prekliate Dedičstvo'!")
			await get_tree().create_timer(2.0).timeout

			# Automatický návrat do hlavného menu
			GameManager.go_to_main_menu()
	elif chapter_number == 1 or chapter_number == 2 or chapter_number == 3 or chapter_number == 4 or chapter_number == 5 or chapter_number == 6:
		if story_phase == 0:
			# Po úvodných dialógoch - ukázať prvý hlavolam
			print("Zobrazujem prvý hlavolam")
			story_phase = 1
			GameManager.update_story_progress(chapter_number, story_phase)
			# Pre Kapitolu 1 - zmeniť pozadie na zašifrovaný list (1_2)
			if chapter_number == 1:
				change_background_image("res://assets/pozadia/Kapitola_1/1_2.png")
			# Pre Kapitolu 2 - zmeniť pozadie na druhý obrázok
			elif chapter_number == 2:
				change_background_image("res://assets/pozadia/Kapitola_2/2.png")
			# Pre Kapitolu 3 - zmeniť pozadie na druhý obrázok (pred prvou hádankou)
			elif chapter_number == 3:
				change_background_image("res://assets/pozadia/Kapitola_3/2.png")
			# Pre Kapitolu 4 - zmeniť pozadie na druhý obrázok (pred prvou hádankou)
			elif chapter_number == 4:
				change_background_image("res://assets/pozadia/Kapitola_4/2.png")
			# Pre Kapitolu 5 - zmeniť pozadie na druhý obrázok (pred prvou hádankou)
			elif chapter_number == 5:
				change_background_image("res://assets/pozadia/Kapitola_5/2.png")
			# Pre Kapitolu 6 - zmeniť pozadie na druhý obrázok (pred prvou hádankou)
			elif chapter_number == 6:
				change_background_image("res://assets/pozadia/Kapitola_6/2.png")
			show_puzzle_button(1)
		elif story_phase == 2:
			# Po dialógoch medzi hlavolamami - ukázať druhý hlavolam
			print("Zobrazujem druhý hlavolam")
			story_phase = 3
			GameManager.update_story_progress(chapter_number, story_phase)
			# Pre Kapitolu 1 - zmeniť pozadie na lesnú križovatku (1_6)
			if chapter_number == 1:
				change_background_image("res://assets/pozadia/Kapitola_1/1_6.png")
			# Pre Kapitolu 2 - zmeniť pozadie na tretí obrázok
			elif chapter_number == 2:
				change_background_image("res://assets/pozadia/Kapitola_2/3.png")
			show_puzzle_button(2)
		elif story_phase == 3:
			# Po záverečných dialógoch - kapitola je dokončená
			print("🎉 Záverečné dialógy dokončené - spúšťam automatický prechod")
			GameManager.update_story_progress(chapter_number, story_phase)
			show_chapter_completion()
	elif current_puzzle > 0:
		# Pre ostatné kapitoly - spustenie hlavolamu
		show_puzzle_scene(current_puzzle)

func show_puzzle_scene(puzzle_number: int):
	# Spustenie puzzle hudby
	print("🧩 Spúšťam puzzle ", puzzle_number, " pre kapitolu ", chapter_number)
	AudioManager.start_puzzle()

	# Pri spustení puzzle vrátiť pozadie na UI_Pozadie.png
	change_background_image("res://assets/Obrázky/UI_Pozadie.png")

	if chapter_number == 1:
		if puzzle_number == 1:
			show_caesar_cipher_puzzle()
		elif puzzle_number == 2:
			show_navigation_puzzle()
	elif chapter_number == 2:
		if puzzle_number == 1:
			show_blood_inscription_puzzle()
		elif puzzle_number == 2:
			show_order_test_puzzle()
	elif chapter_number == 3:
		if puzzle_number == 1:
			show_reversed_message_puzzle()
		elif puzzle_number == 2:
			show_simple_calculation_puzzle()
	elif chapter_number == 4:
		if puzzle_number == 1:
			show_memory_test_puzzle()
		elif puzzle_number == 2:
			show_vampire_arithmetic_puzzle()
	elif chapter_number == 5:
		if puzzle_number == 1:
			show_shadow_code_puzzle()
		elif puzzle_number == 2:
			show_three_levers_puzzle()
	elif chapter_number == 6:
		if puzzle_number == 1:
			show_three_sisters_puzzle()
		elif puzzle_number == 2:
			show_ritual_rhythm_puzzle()
	else:
		# Pre ostatné kapitoly zatiaľ simulácia
		show_simple_puzzle(puzzle_number)

func show_caesar_cipher_puzzle():
	var caesar_scene_resource = load("res://scenes/CaesarCipherPuzzle.tscn")
	if caesar_scene_resource:
		var caesar_scene = caesar_scene_resource.instantiate()
		add_child(caesar_scene)
		caesar_scene.puzzle_solved.connect(_on_caesar_solved)
		caesar_scene.puzzle_failed.connect(_on_puzzle_failed)
		caesar_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať CaesarCipherPuzzle.tscn")
		show_simple_puzzle(1)

func show_navigation_puzzle():
	var navigation_scene_resource = load("res://scenes/NavigationPuzzle.tscn")
	if navigation_scene_resource:
		var navigation_scene = navigation_scene_resource.instantiate()
		add_child(navigation_scene)
		navigation_scene.puzzle_solved.connect(_on_navigation_solved)
		navigation_scene.puzzle_failed.connect(_on_puzzle_failed)
		navigation_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať NavigationPuzzle.tscn")
		show_simple_puzzle(2)

func show_blood_inscription_puzzle():
	var blood_scene_resource = load("res://scenes/BloodInscriptionPuzzle.tscn")
	if blood_scene_resource:
		var blood_scene = blood_scene_resource.instantiate()
		add_child(blood_scene)
		blood_scene.puzzle_solved.connect(_on_blood_inscription_solved)
		blood_scene.puzzle_failed.connect(_on_puzzle_failed)
		blood_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať BloodInscriptionPuzzle.tscn")
		show_simple_puzzle(1)

func show_order_test_puzzle():
	var order_scene_resource = load("res://scenes/OrderTestPuzzle.tscn")
	if order_scene_resource:
		var order_scene = order_scene_resource.instantiate()
		add_child(order_scene)
		order_scene.puzzle_solved.connect(_on_order_test_solved)
		order_scene.puzzle_failed.connect(_on_puzzle_failed)
		order_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať OrderTestPuzzle.tscn")
		show_simple_puzzle(2)

func show_reversed_message_puzzle():
	var reversed_scene_resource = load("res://scenes/ReversedMessagePuzzle.tscn")
	if reversed_scene_resource:
		var reversed_scene = reversed_scene_resource.instantiate()
		add_child(reversed_scene)
		reversed_scene.puzzle_solved.connect(_on_reversed_message_solved)
		reversed_scene.puzzle_failed.connect(_on_puzzle_failed)
		reversed_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať ReversedMessagePuzzle.tscn")
		show_simple_puzzle(1)

func show_simple_calculation_puzzle():
	var calculation_scene_resource = load("res://scenes/SimpleCalculationPuzzle.tscn")
	if calculation_scene_resource:
		var calculation_scene = calculation_scene_resource.instantiate()
		add_child(calculation_scene)
		calculation_scene.puzzle_solved.connect(_on_simple_calculation_solved)
		calculation_scene.puzzle_failed.connect(_on_puzzle_failed)
		calculation_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať SimpleCalculationPuzzle.tscn")
		show_simple_puzzle(2)

func show_memory_test_puzzle():
	var memory_scene_resource = load("res://scenes/MemoryTestPuzzle.tscn")
	if memory_scene_resource:
		var memory_scene = memory_scene_resource.instantiate()
		add_child(memory_scene)
		memory_scene.puzzle_solved.connect(_on_memory_test_solved)
		memory_scene.puzzle_failed.connect(_on_puzzle_failed)
		memory_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať MemoryTestPuzzle.tscn")
		show_simple_puzzle(1)

func show_vampire_arithmetic_puzzle():
	var vampire_scene_resource = load("res://scenes/VampireArithmeticPuzzle.tscn")
	if vampire_scene_resource:
		var vampire_scene = vampire_scene_resource.instantiate()
		add_child(vampire_scene)
		vampire_scene.puzzle_solved.connect(_on_vampire_arithmetic_solved)
		vampire_scene.puzzle_failed.connect(_on_puzzle_failed)
		vampire_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať VampireArithmeticPuzzle.tscn")
		show_simple_puzzle(2)

func show_shadow_code_puzzle():
	var shadow_scene_resource = load("res://scenes/ShadowCodePuzzle.tscn")
	if shadow_scene_resource:
		var shadow_scene = shadow_scene_resource.instantiate()
		add_child(shadow_scene)
		shadow_scene.puzzle_solved.connect(_on_shadow_code_solved)
		shadow_scene.puzzle_failed.connect(_on_puzzle_failed)
		shadow_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať ShadowCodePuzzle.tscn")
		show_simple_puzzle(1)

func show_three_levers_puzzle():
	var levers_scene_resource = load("res://scenes/ThreeLeverssPuzzle.tscn")
	if levers_scene_resource:
		var levers_scene = levers_scene_resource.instantiate()
		add_child(levers_scene)
		levers_scene.puzzle_solved.connect(_on_three_levers_solved)
		levers_scene.puzzle_failed.connect(_on_puzzle_failed)
		levers_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať ThreeLeverssPuzzle.tscn")
		show_simple_puzzle(2)

func show_three_sisters_puzzle():
	var sisters_scene_resource = load("res://scenes/ThreeSistersPuzzle.tscn")
	if sisters_scene_resource:
		var sisters_scene = sisters_scene_resource.instantiate()
		add_child(sisters_scene)
		sisters_scene.puzzle_solved.connect(_on_three_sisters_solved)
		sisters_scene.puzzle_failed.connect(_on_puzzle_failed)
		sisters_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať ThreeSistersPuzzle.tscn")
		show_simple_puzzle(1)

func show_ritual_rhythm_puzzle():
	var ritual_scene_resource = load("res://scenes/RitualRhythmPuzzle.tscn")
	if ritual_scene_resource:
		var ritual_scene = ritual_scene_resource.instantiate()
		add_child(ritual_scene)
		ritual_scene.puzzle_solved.connect(_on_ritual_rhythm_solved)
		ritual_scene.puzzle_failed.connect(_on_puzzle_failed)
		ritual_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať RitualRhythmPuzzle.tscn")
		show_simple_puzzle(2)

func _on_caesar_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is CaesarCipherPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	AudioManager.return_from_puzzle()

	# Pre kapitolu 1 - pokračovať v príbehu
	if chapter_number == 1:
		story_phase = 2
		hide_puzzle_buttons()
		# Vrátiť pozadie na kočiša (1_3) - "To je všetko! Ďalej nejdem, páni!"
		change_background_image("res://assets/pozadia/Kapitola_1/1_3.png")
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		complete_puzzle(1)

func _on_navigation_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is NavigationPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	AudioManager.return_from_puzzle()

	# Pre kapitolu 1 - vrátiť pozadie na lesnú križovatku (1_6) a dokončiť kapitolu
	if chapter_number == 1:
		# Vrátiť pozadie na lesnú križovatku (1_6) - pozadie pred puzzle
		change_background_image("res://assets/pozadia/Kapitola_1/1_6.png")
		complete_puzzle(2)
	else:
		complete_puzzle(2)

func _on_blood_inscription_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is BloodInscriptionPuzzle:
			child.queue_free()

	# Pre kapitolu 2 - pokračovať v príbehu
	if chapter_number == 2:
		story_phase = 2
		hide_puzzle_buttons()
		# Vrátiť pozadie na druhý obrázok (po prvom puzzle)
		change_background_image("res://assets/pozadia/Kapitola_2/2.png")
		# Spustiť Viktor's theme keď sa Viktor predstaví (bez return_from_puzzle)
		AudioManager.meet_viktor()
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		# Pre ostatné kapitoly - vrátiť sa k predchádzajúcej hudbe
		return_from_puzzle()
		complete_puzzle(1)

func _on_order_test_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is OrderTestPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 2 - dokončiť kapitolu
	if chapter_number == 2:
		# Vrátiť pozadie na tretí obrázok (po druhom puzzle)
		change_background_image("res://assets/pozadia/Kapitola_2/3.png")
		complete_puzzle(2)
	else:
		complete_puzzle(2)

func _on_reversed_message_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is ReversedMessagePuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 3 - pokračovať v príbehu
	if chapter_number == 3:
		story_phase = 2
		hide_puzzle_buttons()
		# Vrátiť pozadie na 2.png (pozadie pred prvou hádankou)
		change_background_image("res://assets/pozadia/Kapitola_3/2.png")
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		complete_puzzle(1)

func _on_simple_calculation_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is SimpleCalculationPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 3 - dokončiť kapitolu
	if chapter_number == 3:
		# Vrátiť pozadie na 3.png (pozadie pred druhou hádankou)
		change_background_image("res://assets/pozadia/Kapitola_3/3.png")
		complete_puzzle(2)
	else:
		complete_puzzle(2)

func _on_memory_test_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is MemoryTestPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 4 - pokračovať v príbehu
	if chapter_number == 4:
		story_phase = 2
		hide_puzzle_buttons()
		# Vrátiť pozadie na 2.png (pozadie pred prvou hádankou)
		change_background_image("res://assets/pozadia/Kapitola_4/2.png")
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		complete_puzzle(1)

func _on_vampire_arithmetic_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is VampireArithmeticPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 4 - dokončiť kapitolu
	if chapter_number == 4:
		# Vrátiť pozadie na 1.png (pozadie pred druhou hádankou)
		change_background_image("res://assets/pozadia/Kapitola_4/1.png")
		complete_puzzle(2)
	else:
		complete_puzzle(2)

func _on_shadow_code_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is ShadowCodePuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 5 - pokračovať v príbehu
	if chapter_number == 5:
		story_phase = 2
		hide_puzzle_buttons()
		# Vrátiť pozadie na 2.png (pozadie pred prvou hádankou)
		change_background_image("res://assets/pozadia/Kapitola_5/2.png")
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		complete_puzzle(1)

func _on_three_levers_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is ThreeLeverssPuzzle:
			child.queue_free()

	# Pre kapitolu 5 - dokončiť kapitolu a spustiť ancient crypts hudbu
	if chapter_number == 5:
		# Vrátiť pozadie na 3.png (pozadie pred druhou hádankou)
		change_background_image("res://assets/pozadia/Kapitola_5/3.png")
		# Spustiť ancient crypts hudbu pre vstup do krypt (bez return_from_puzzle)
		AudioManager.enter_crypts()
		complete_puzzle(2)
	else:
		# Pre ostatné kapitoly - vrátiť sa k predchádzajúcej hudbe
		return_from_puzzle()
		complete_puzzle(2)

func _on_three_sisters_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is ThreeSistersPuzzle:
			child.queue_free()

	# Pre kapitolu 6 - pokračovať v príbehu
	if chapter_number == 6:
		story_phase = 2
		hide_puzzle_buttons()
		# Vrátiť pozadie na 2.png (pozadie pred prvou hádankou)
		change_background_image("res://assets/pozadia/Kapitola_6/2.png")
		# Spustiť final battle hudbu pre finálny rituál (bez return_from_puzzle)
		AudioManager.final_battle()
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		# Pre ostatné kapitoly - vrátiť sa k predchádzajúcej hudbe
		return_from_puzzle()
		complete_puzzle(1)

func _on_ritual_rhythm_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is RitualRhythmPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 6 - dokončiť hru
	if chapter_number == 6:
		# Vrátiť pozadie na 3.png (pozadie pred druhou hádankou)
		change_background_image("res://assets/pozadia/Kapitola_6/3.png")
		complete_puzzle(2)
	else:
		complete_puzzle(2)

func _on_puzzle_failed():
	current_puzzle = 0
	# Odstránenie puzzle scén
	for child in get_children():
		if child is CaesarCipherPuzzle or child is NavigationPuzzle or child is BloodInscriptionPuzzle or child is OrderTestPuzzle or child is ReversedMessagePuzzle or child is SimpleCalculationPuzzle or child is MemoryTestPuzzle or child is VampireArithmeticPuzzle or child is ShadowCodePuzzle or child is ThreeLeverssPuzzle or child is ThreeSistersPuzzle or child is RitualRhythmPuzzle:
			child.queue_free()

func show_simple_puzzle(puzzle_number: int):
	# Jednoduchý dialóg pre simuláciu hlavolamu (pre ostatné kapitoly)
	var puzzle_scene = AcceptDialog.new()
	add_child(puzzle_scene)

	var chapter_info = GameManager.chapter_info[chapter_number]
	var puzzle_name = chapter_info.puzzles[puzzle_number - 1]

	puzzle_scene.dialog_text = "Hlavolam: " + puzzle_name + "\n\nToto je simulácia hlavolamu.\nV skutočnej hre by tu bola interaktívna logika.\n\nChcete označiť tento hlavolam ako dokončený?"
	puzzle_scene.title = "Hlavolam " + str(puzzle_number)

	# Pridanie tlačidiel
	puzzle_scene.add_button("Dokončiť", true, "complete")
	puzzle_scene.add_button("Zrušiť", false, "cancel")

	puzzle_scene.custom_action.connect(_on_puzzle_action)
	puzzle_scene.popup_centered()

func _on_puzzle_action(action: String):
	if action == "complete":
		complete_puzzle(current_puzzle)
	current_puzzle = 0

func complete_puzzle(puzzle_number: int):
	# Označenie hlavolamu ako dokončený
	GameManager.complete_puzzle(chapter_number, puzzle_number)

	# Aktualizovanie story progress
	GameManager.update_story_progress(chapter_number, story_phase)

	# Aktualizovanie UI
	update_puzzle_buttons()

	# Pre kapitoly 1 a 2 - špeciálne správanie
	if chapter_number == 1:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	elif chapter_number == 2:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	elif chapter_number == 3:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	elif chapter_number == 4:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	elif chapter_number == 5:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	elif chapter_number == 6:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy (koniec hry)
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	else:
		# Pre ostatné kapitoly - štandardné správanie
		if dialogue_system:
			var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
			dialogue_system.start_dialogue(success_dialogue)

	# Kontrola, či sú dokončené oba hlavolamy
	if GameManager.is_puzzle_completed(chapter_number, 1) and GameManager.is_puzzle_completed(chapter_number, 2):
		print("🎉 Oba hlavolamy dokončené v kapitole ", chapter_number)

		# Kapitola je dokončená - automatický prechod
		if puzzle_number == 2:
			# Po druhom hlavolame - spustiť záverečné dialógy ak existujú
			if dialogue_system and chapter_number <= 6:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				if success_dialogue and success_dialogue.size() > 0:
					print("🎭 Spúšťam záverečné dialógy pre kapitolu ", chapter_number)

					# Pripojenie signálu pre automatický prechod
					if not dialogue_system.dialogue_finished.is_connected(_on_final_dialogue_finished):
						dialogue_system.dialogue_finished.connect(_on_final_dialogue_finished, CONNECT_ONE_SHOT)

					dialogue_system.start_dialogue(success_dialogue)
				else:
					# Žiadne dialógy - okamžitý automatický prechod
					print("⚡ Žiadne dialógy - okamžitý prechod")
					show_chapter_completion()
			else:
				# Žiadne dialógy - okamžitý automatický prechod
				print("⚡ Kapitola 7 alebo žiadne dialógy - okamžitý prechod")
				show_chapter_completion()
		else:
			# Po prvom hlavolame - čakať na druhý
			pass

func show_chapter_completion():
	print("🎯 Spúšťam automatické dokončenie kapitoly ", chapter_number)
	await get_tree().create_timer(2.0).timeout

	# Skontrolovať, či existuje ďalšia kapitola
	var next_chapter = chapter_number + 1
	var has_next_chapter = next_chapter <= 7 and GameManager.chapter_info.has(next_chapter)

	if has_next_chapter:
		# Úplne automatický plynulý prechod na ďalšiu kapitolu
		print("✅ Kapitola ", chapter_number, " dokončená - automatický prechod na kapitolu ", next_chapter)

		# Krátke zobrazenie úspešného dokončenia
		show_completion_message("Kapitola " + str(chapter_number) + " dokončená!")
		await get_tree().create_timer(1.0).timeout

		# Automatický prechod bez dialógov
		print("🚀 Prechod na kapitolu ", next_chapter)
		GameManager.go_to_chapter(next_chapter)
	else:
		# Ak nie je ďalšia kapitola (kapitola 7 - epilóg), vrátiť sa do menu
		print("🎉 Hra dokončená - návrat do hlavného menu")

		# Krátke zobrazenie dokončenia hry
		show_completion_message("Hra dokončená! Ďakujeme za hranie!")
		await get_tree().create_timer(2.0).timeout

		# Automatický návrat do hlavného menu
		GameManager.go_to_main_menu()

func show_completion_message(message: String):
	"""Zobrazí krátku správu o dokončení bez dialógu"""
	var label = Label.new()
	label.text = message
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER

	# Aplikovanie fontu
	FontLoader.apply_font_style(label, "chapter_title")
	label.add_theme_color_override("font_color", Color("#D4AF37"))
	label.add_theme_font_size_override("font_size", 32)

	# Pozicionovanie na stred obrazovky
	label.anchors_preset = Control.PRESET_FULL_RECT
	label.grow_horizontal = Control.GROW_DIRECTION_BOTH
	label.grow_vertical = Control.GROW_DIRECTION_BOTH

	# Pridanie do scény
	add_child(label)

	# Animácia fade in/out
	label.modulate.a = 0.0
	var tween = create_tween()
	tween.tween_property(label, "modulate:a", 1.0, 0.5)
	tween.tween_interval(0.5)  # Nahradené tween_delay za tween_interval
	tween.tween_property(label, "modulate:a", 0.0, 0.5)
	tween.tween_callback(func(): label.queue_free())

func _on_final_dialogue_finished():
	# Záverečné dialógy skončili
	print("🎬 _on_final_dialogue_finished volaná pre kapitolu ", chapter_number)

	if chapter_number == 1:
		# Pre kapitolu 1 - zmeniť pozadie na zámok Van Helsinga (1_4)
		print("Kapitola 1 - záverečné dialógy dokončené, mením pozadie na zámok")
		change_background_image("res://assets/pozadia/Kapitola_1/1_4.png")

		# Automatické dokončenie kapitoly
		print("🚀 Spúšťam show_chapter_completion() pre kapitolu ", chapter_number)
		show_chapter_completion()
	elif chapter_number == 6:
		# Pre kapitolu 6 - úplne automatický prechod na epilóg
		print("Kapitola 6 dokončená - automatický prechod na epilóg")

		# Krátke zobrazenie dokončenia
		show_completion_message("Kapitola 6 dokončená! Prechod na epilóg...")
		await get_tree().create_timer(1.0).timeout

		# Automatický prechod na epilóg
		GameManager.go_to_chapter(7)
	else:
		# Pre ostatné kapitoly - automatické dokončenie
		print("🚀 Spúšťam show_chapter_completion() pre kapitolu ", chapter_number)
		show_chapter_completion()



func _on_return_to_menu():
	# Vrátiť sa do hlavného menu
	GameManager.go_to_main_menu()

func _on_start_pressed():
	# Pre kapitolu 7 - spustiť epilóg dialógy
	if chapter_number == 7:
		story_phase = 0
		start_chapter_intro()

func start_chapter_music():
	"""Spustí príslušnú hudbu pre kapitolu"""
	print("🎵 start_chapter_music() volaná pre kapitolu: ", chapter_number)
	match chapter_number:
		1:
			print("🎵 Volám AudioManager.start_chapter_1()")
			AudioManager.start_chapter_1()
		2:
			print("🎵 Volám AudioManager.start_chapter_2()")
			AudioManager.start_chapter_2()
		3:
			print("🎵 Volám AudioManager.start_chapter_3()")
			AudioManager.start_chapter_3()
		4:
			print("🎵 Volám AudioManager.start_chapter_4()")
			AudioManager.start_chapter_4()
		5:
			print("🎵 Volám AudioManager.start_chapter_5()")
			AudioManager.start_chapter_5()
		6:
			print("🎵 Volám AudioManager.start_chapter_6()")
			AudioManager.start_chapter_6()
		7:
			print("🎵 Volám AudioManager.start_epilogue()")
			AudioManager.start_epilogue()
		_:
			# Fallback na library secrets
			print("🎵 Fallback - volám AudioManager.play_music('library_secrets')")
			AudioManager.play_music("library_secrets")

func return_from_puzzle():
	"""Univerzálna funkcia pre návrat z puzzle - obnoví hudbu"""
	AudioManager.return_from_puzzle()

func set_story_phase(phase: int):
	"""Nastaví story_phase pre obnovu stavu"""
	story_phase = phase
	print("📖 Nastavujem story_phase na: ", phase)

	# Aktualizovať UI podľa fázy
	match phase:
		0:
			hide_puzzle_buttons()
		1:
			show_puzzle_button(1)
		2:
			show_puzzle_button(2)
		3:
			# Kapitola dokončená
			pass

func _input(event):
	if event.is_action_pressed("ui_cancel") and (not dialogue_system or not dialogue_system.visible):
		GameManager.go_to_main_menu()
