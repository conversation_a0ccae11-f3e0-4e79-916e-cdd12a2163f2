extends Control
class_name ShadowCodePuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var candles_label: RichTextLabel = $PuzzlePanel/VBoxContainer/CandlesLabel
@onready var clue_label: Label = $PuzzlePanel/VBoxContainer/ClueLabel
@onready var code_field: LineEdit = $PuzzlePanel/VBoxContainer/CodeContainer/CodeField
@onready var submit_button: TextureButton = $PuzzlePanel/VBoxContainer/CodeContainer/SubmitButton
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_code: String = "7539"  # 7+5=12, 3+9=12
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()
	
	# Pripojenie signálov
	if submit_button:
		submit_button.pressed.connect(_on_submit_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)
	if code_field:
		code_field.text_submitted.connect(_on_code_submitted)

func show_puzzle():
	show()
	reset_puzzle()
	if code_field:
		code_field.grab_focus()

func reset_puzzle():
	hint_level = 0
	if code_field:
		code_field.text = ""
		code_field.placeholder_text = "Zadajte 4-ciferný kód..."

func _on_submit_pressed():
	check_code()

func _on_code_submitted(text: String):
	check_code()

func check_code():
	if not code_field:
		return
	
	var player_code = code_field.text.strip_edges()
	
	if player_code.is_empty():
		show_feedback("Zadajte kód!", false)
		return
	
	if player_code.length() != 4:
		show_feedback("Kód musí mať 4 číslice!", false)
		return
	
	if not player_code.is_valid_int():
		show_feedback("Zadajte platné čísla!", false)
		return
	
	if player_code == correct_code:
		AudioManager.play_puzzle_success_sound()
		show_feedback("Výborne! Kód je správny!", true)
		await get_tree().create_timer(1.5).timeout
		puzzle_solved.emit()
		hide()
	else:
		AudioManager.play_puzzle_error_sound()
		show_feedback("Nesprávny kód. Skúste znova.", false)
		if code_field:
			code_field.text = ""

func show_feedback(message: String, success: bool):
	if code_field:
		if success:
			code_field.modulate = Color.GREEN
		else:
			code_field.modulate = Color.RED
		
		# Zobraz správu v placeholder
		code_field.placeholder_text = message
		
		var tween = create_tween()
		tween.tween_property(code_field, "modulate", Color.WHITE, 1.0)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Tiene nikdy neklamú, ale musíš ich správne spárovať."
		2:
			return "Tucet je 12. Ktoré dve čísla dávajú dokopy 12?"
		3:
			return "7+5=12 a 3+9=12. Zadaj všetky štyri čísla: 7539"
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(text: String):
	# Jednoduchý dialóg s nápoveďou
	if code_field:
		code_field.placeholder_text = "Nápoveda: " + text
		await get_tree().create_timer(4.0).timeout
		code_field.placeholder_text = "Zadajte 4-ciferný kód..."

func _on_close_pressed():
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
