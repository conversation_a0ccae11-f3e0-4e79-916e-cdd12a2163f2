extends Control
class_name BloodInscriptionPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var inscription_label: Label = $PuzzlePanel/VBoxContainer/InscriptionLabel
@onready var letters_container: GridContainer = $PuzzlePanel/VBoxContainer/LettersContainer
@onready var sequence_label: Label = $PuzzlePanel/VBoxContainer/SequenceLabel
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var reset_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/ResetButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_sequence: Array[String] = ["T", "R", "M", "S", "O"]
var current_sequence: Array[String] = []
var letter_buttons: Array[Button] = []
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()
	
	# Pripojenie signálov
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if reset_button:
		reset_button.pressed.connect(_on_reset_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)
	
	setup_puzzle()

func setup_puzzle():
	if title_label:
		title_label.text = "Krvavý nápis na bráne"
	
	if description_label:
		description_label.text = "[center]Na bráne je krvou napísaný text.[/center]\n\nRozlúštite správne poradie písmen:"
	
	if inscription_label:
		inscription_label.text = "MOJE MENO JE SMRŤ, ALE POZADU SOM ŽIVOT"
		inscription_label.add_theme_font_size_override("font_size", 18)
		inscription_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		inscription_label.add_theme_color_override("font_color", Color(0.8, 0.2, 0.2))  # Krvavá červená
	
	create_letter_buttons()
	update_sequence_display()

func create_letter_buttons():
	if not letters_container:
		return
	
	# Vyčistiť existujúce tlačidlá
	for child in letters_container.get_children():
		child.queue_free()
	
	letter_buttons.clear()
	
	# Nastavenie grid kontajnera
	letters_container.columns = 5
	
	# Vytvorenie tlačidiel pre písmená T, R, M, S, O v náhodnom poradí
	var letters = ["T", "R", "M", "S", "O"]
	letters.shuffle()
	
	for letter in letters:
		var button = Button.new()
		button.text = letter
		button.custom_minimum_size = Vector2(80, 80)
		button.add_theme_font_size_override("font_size", 24)
		
		# Pripojenie signálu
		button.pressed.connect(_on_letter_pressed.bind(letter))
		
		letters_container.add_child(button)
		letter_buttons.append(button)

func show_puzzle():
	show()
	if letter_buttons.size() > 0:
		letter_buttons[0].grab_focus()

func _on_letter_pressed(letter: String):
	current_sequence.append(letter)
	update_sequence_display()
	
	# Kontrola správnosti
	if current_sequence.size() <= correct_sequence.size():
		if current_sequence[current_sequence.size() - 1] != correct_sequence[current_sequence.size() - 1]:
			# Nesprávne písmeno
			AudioManager.play_puzzle_error_sound()
			show_error_feedback()
			await get_tree().create_timer(1.0).timeout
			reset_sequence()
		elif current_sequence.size() == correct_sequence.size():
			# Hlavolam vyriešený
			AudioManager.play_puzzle_success_sound()
			puzzle_solved.emit()
			hide()

func update_sequence_display():
	if sequence_label:
		var display_text = "Vaša sekvencia: "
		for i in range(current_sequence.size()):
			display_text += current_sequence[i]
			if i < current_sequence.size() - 1:
				display_text += " → "
		
		if current_sequence.size() < correct_sequence.size():
			display_text += " → ?"
		
		sequence_label.text = display_text

func show_error_feedback():
	# Červené zablikanie tlačidiel
	for button in letter_buttons:
		if button:
			button.modulate = Color.RED
	
	var tween = create_tween()
	for button in letter_buttons:
		if button:
			tween.parallel().tween_property(button, "modulate", Color.WHITE, 0.5)

func reset_sequence():
	current_sequence.clear()
	update_sequence_display()

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Čítaj pozorne - niekedy je odpoveď v opaku."
		2:
			return "Smrť pozadu... čo dostaneš, keď otočíš slovo?"
		3:
			return "SMRŤ odzadu je TRMS, ale nezabudni na piatu literu z 'SOM' → TRMSO"
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(hint_text: String):
	var dialog = AcceptDialog.new()
	add_child(dialog)
	dialog.dialog_text = hint_text
	dialog.title = "Nápoveda"
	dialog.popup_centered()
	
	# Automatické odstránenie dialógu
	dialog.confirmed.connect(func(): dialog.queue_free())
	dialog.close_requested.connect(func(): dialog.queue_free())

func _on_reset_pressed():
	reset_sequence()

func _on_close_pressed():
	puzzle_failed.emit()
	hide()



func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
